{"_module": "pandapower.auxiliary", "_class": "pandapowerNet", "_object": {"bus": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"vn_kv\",\"type\",\"zone\",\"in_service\",\"min_vm_pu\",\"max_vm_pu\"],\"index\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13],\"data\":[[1,138.0,\"b\",1.0,true,0.94,1.06],[2,138.0,\"b\",1.0,true,0.94,1.06],[3,138.0,\"b\",1.0,true,0.94,1.06],[4,138.0,\"b\",1.0,true,0.94,1.06],[5,138.0,\"b\",1.0,true,0.94,1.06],[6,20.0,\"b\",1.0,true,0.94,1.06],[7,14.0,\"b\",1.0,true,0.94,1.06],[8,12.0,\"b\",1.0,true,0.94,1.06],[9,20.0,\"b\",1.0,true,0.94,1.06],[10,20.0,\"b\",1.0,true,0.94,1.06],[11,20.0,\"b\",1.0,true,0.94,1.06],[12,20.0,\"b\",1.0,true,0.94,1.06],[13,20.0,\"b\",1.0,true,0.94,1.06],[14,20.0,\"b\",1.0,true,0.94,1.06]]}", "orient": "split", "dtype": {"name": "object", "vn_kv": "float64", "type": "object", "zone": "object", "in_service": "bool", "min_vm_pu": "float64", "max_vm_pu": "float64"}}, "load": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"p_mw\",\"q_mvar\",\"const_z_percent\",\"const_i_percent\",\"sn_mva\",\"scaling\",\"in_service\",\"type\",\"controllable\"],\"index\":[0,1,2,3,4,5,6,7,8,9,10],\"data\":[[null,1,21.699999999999999,12.699999999999999,0.0,0.0,null,1.0,true,null,false],[null,2,94.200000000000003,19.0,0.0,0.0,null,1.0,true,null,false],[null,3,47.799999999999997,-3.9,0.0,0.0,null,1.0,true,null,false],[null,4,7.6,1.6,0.0,0.0,null,1.0,true,null,false],[null,5,11.199999999999999,7.5,0.0,0.0,null,1.0,true,null,false],[null,8,29.5,16.600000000000001,0.0,0.0,null,1.0,true,null,false],[null,9,9.0,5.8,0.0,0.0,null,1.0,true,null,false],[null,10,3.5,1.8,0.0,0.0,null,1.0,true,null,false],[null,11,6.1,1.6,0.0,0.0,null,1.0,true,null,false],[null,12,13.5,5.8,0.0,0.0,null,1.0,true,null,false],[null,13,14.9,5.0,0.0,0.0,null,1.0,true,null,false]]}", "orient": "split", "dtype": {"name": "object", "bus": "uint32", "p_mw": "float64", "q_mvar": "float64", "const_z_percent": "float64", "const_i_percent": "float64", "sn_mva": "float64", "scaling": "float64", "in_service": "bool", "type": "object", "controllable": "object"}}, "sgen": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"p_mw\",\"q_mvar\",\"sn_mva\",\"scaling\",\"in_service\",\"type\",\"current_source\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "bus": "int64", "p_mw": "float64", "q_mvar": "float64", "sn_mva": "float64", "scaling": "float64", "in_service": "bool", "type": "object", "current_source": "bool"}}, "motor": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"pn_mech_mw\",\"loading_percent\",\"cos_phi\",\"cos_phi_n\",\"efficiency_percent\",\"efficiency_n_percent\",\"lrc_pu\",\"vn_kv\",\"scaling\",\"in_service\",\"rx\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "bus": "int64", "pn_mech_mw": "float64", "loading_percent": "float64", "cos_phi": "float64", "cos_phi_n": "float64", "efficiency_percent": "float64", "efficiency_n_percent": "float64", "lrc_pu": "float64", "vn_kv": "float64", "scaling": "float64", "in_service": "bool", "rx": "float64"}}, "asymmetric_load": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"p_a_mw\",\"q_a_mvar\",\"p_b_mw\",\"q_b_mvar\",\"p_c_mw\",\"q_c_mvar\",\"sn_mva\",\"scaling\",\"in_service\",\"type\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "bus": "uint32", "p_a_mw": "float64", "q_a_mvar": "float64", "p_b_mw": "float64", "q_b_mvar": "float64", "p_c_mw": "float64", "q_c_mvar": "float64", "sn_mva": "float64", "scaling": "float64", "in_service": "bool", "type": "object"}}, "asymmetric_sgen": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"p_a_mw\",\"q_a_mvar\",\"p_b_mw\",\"q_b_mvar\",\"p_c_mw\",\"q_c_mvar\",\"sn_mva\",\"scaling\",\"in_service\",\"type\",\"current_source\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "bus": "int64", "p_a_mw": "float64", "q_a_mvar": "float64", "p_b_mw": "float64", "q_b_mvar": "float64", "p_c_mw": "float64", "q_c_mvar": "float64", "sn_mva": "float64", "scaling": "float64", "in_service": "bool", "type": "object", "current_source": "bool"}}, "storage": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"p_mw\",\"q_mvar\",\"sn_mva\",\"soc_percent\",\"min_e_mwh\",\"max_e_mwh\",\"scaling\",\"in_service\",\"type\"],\"index\":[0,1],\"data\":[[null,5,0.0,0.0,null,null,0.0,15.0,1.0,true,null],[null,7,0.0,0.0,null,null,0.0,7.0,1.0,true,null]]}", "orient": "split", "dtype": {"name": "object", "bus": "int64", "p_mw": "float64", "q_mvar": "float64", "sn_mva": "float64", "soc_percent": "float64", "min_e_mwh": "float64", "max_e_mwh": "float64", "scaling": "float64", "in_service": "bool", "type": "object"}}, "gen": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"p_mw\",\"vm_pu\",\"sn_mva\",\"min_q_mvar\",\"max_q_mvar\",\"scaling\",\"slack\",\"in_service\",\"type\",\"controllable\",\"min_p_mw\",\"max_p_mw\",\"slack_weight\",\"power_station_trafo\"],\"index\":[0,1,2,3,4,5],\"data\":[[null,1,40.0,1.045,null,-40.0,50.0,1.0,false,true,null,true,0.0,140.0,0.0,null],[null,2,0.0,1.01,null,0.0,40.0,1.0,false,true,null,true,0.0,100.0,0.0,null],[null,5,0.0,1.07,null,-6.0,24.0,1.0,false,true,null,true,0.0,100.0,0.0,null],[null,5,0.0,1.07,null,-6.0,24.0,1.0,false,true,null,true,0.0,100.0,0.0,null],[null,7,0.0,1.09,null,-6.0,24.0,1.0,false,true,null,true,0.0,100.0,0.0,null],[\"gen_0_5\",0,-219.0,1.06,null,-9999.0,9999.0,1.0,true,true,null,true,null,null,1.0,null]]}", "orient": "split", "dtype": {"name": "object", "bus": "uint32", "p_mw": "float64", "vm_pu": "float64", "sn_mva": "float64", "min_q_mvar": "float64", "max_q_mvar": "float64", "scaling": "float64", "slack": "bool", "in_service": "bool", "type": "object", "controllable": "object", "min_p_mw": "float64", "max_p_mw": "float64", "slack_weight": "float64", "power_station_trafo": "float64"}}, "switch": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"bus\",\"element\",\"et\",\"type\",\"closed\",\"name\",\"z_ohm\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"bus": "int64", "element": "int64", "et": "object", "type": "object", "closed": "bool", "name": "object", "z_ohm": "float64"}}, "shunt": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"bus\",\"name\",\"q_mvar\",\"p_mw\",\"vn_kv\",\"step\",\"max_step\",\"in_service\"],\"index\":[0],\"data\":[[8,null,-19.0,0.0,20.0,1,1,true]]}", "orient": "split", "dtype": {"bus": "uint32", "name": "object", "q_mvar": "float64", "p_mw": "float64", "vn_kv": "float64", "step": "uint32", "max_step": "uint32", "in_service": "bool"}}, "line": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"std_type\",\"from_bus\",\"to_bus\",\"length_km\",\"r_ohm_per_km\",\"x_ohm_per_km\",\"c_nf_per_km\",\"g_us_per_km\",\"max_i_ka\",\"df\",\"parallel\",\"type\",\"in_service\",\"max_loading_percent\"],\"index\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14],\"data\":[[null,null,0,1,1.0,3.6907272,11.2683348,882.522683811391971,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,0,4,1.0,10.2894732,42.475737599999995,822.350682642433412,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,1,2,1.0,8.948775599999999,37.701406800000001,732.092680888995574,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,1,3,1.0,11.0664684,33.578380799999998,568.29112215127509,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,1,4,1.0,10.845558,33.1137072,578.319789012768069,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,2,3,1.0,12.761384400000001,32.570953199999998,213.94489304518595,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,3,4,1.0,2.542374,8.019428400000001,0.0,0.0,41.418606267951418,1.0,1,\"ol\",true,100.0],[null,null,5,10,1.0,0.37992,0.7956,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,5,11,1.0,0.49164,1.02324,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,5,12,1.0,0.2646,0.52108,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,8,9,1.0,0.12724,0.338,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,8,13,1.0,0.50844,1.08152,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,9,10,1.0,0.3282,0.76828,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,11,12,1.0,0.88368,0.79952,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0],[null,null,12,13,1.0,0.68372,1.39208,0.0,0.0,285.788383248864761,1.0,1,\"ol\",true,100.0]]}", "orient": "split", "dtype": {"name": "object", "std_type": "object", "from_bus": "uint32", "to_bus": "uint32", "length_km": "float64", "r_ohm_per_km": "float64", "x_ohm_per_km": "float64", "c_nf_per_km": "float64", "g_us_per_km": "float64", "max_i_ka": "float64", "df": "float64", "parallel": "uint32", "type": "object", "in_service": "bool", "max_loading_percent": "float64"}}, "trafo": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"std_type\",\"hv_bus\",\"lv_bus\",\"sn_mva\",\"vn_hv_kv\",\"vn_lv_kv\",\"vk_percent\",\"vkr_percent\",\"pfe_kw\",\"i0_percent\",\"shift_degree\",\"tap_side\",\"tap_neutral\",\"tap_min\",\"tap_max\",\"tap_step_percent\",\"tap_step_degree\",\"tap_pos\",\"tap_phase_shifter\",\"parallel\",\"df\",\"in_service\",\"max_loading_percent\"],\"index\":[0,1,2,3,4],\"data\":[[null,null,3,6,9900.0,138.0,14.0,2070.288000000000011,0.0,0.0,0.0,0.0,\"hv\",0,null,null,2.200000000000002,0.0,-1,false,1,1.0,true,100.0],[null,null,3,8,9900.0,138.0,20.0,5506.181999999999789,0.0,0.0,0.0,0.0,\"hv\",0,null,null,3.100000000000003,0.0,-1,false,1,1.0,true,100.0],[null,null,4,5,9900.0,138.0,20.0,2494.998000000000047,0.0,0.0,0.0,0.0,\"hv\",0,null,null,6.799999999999995,0.0,-1,false,1,1.0,true,100.0],[null,null,6,7,9900.0,14.0,12.0,1743.884999999999991,0.0,0.0,0.0,0.0,false,0,null,null,0.0,0.0,0,false,1,1.0,true,100.0],[null,null,8,6,9900.0,20.0,14.0,1089.098999999999933,0.0,0.0,0.0,0.0,false,0,null,null,0.0,0.0,0,false,1,1.0,true,100.0]]}", "orient": "split", "dtype": {"name": "object", "std_type": "object", "hv_bus": "uint32", "lv_bus": "uint32", "sn_mva": "float64", "vn_hv_kv": "float64", "vn_lv_kv": "float64", "vk_percent": "float64", "vkr_percent": "float64", "pfe_kw": "float64", "i0_percent": "float64", "shift_degree": "float64", "tap_side": "object", "tap_neutral": "int32", "tap_min": "float64", "tap_max": "float64", "tap_step_percent": "float64", "tap_step_degree": "float64", "tap_pos": "int32", "tap_phase_shifter": "bool", "parallel": "uint32", "df": "float64", "in_service": "bool", "max_loading_percent": "float64"}}, "trafo3w": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"std_type\",\"hv_bus\",\"mv_bus\",\"lv_bus\",\"sn_hv_mva\",\"sn_mv_mva\",\"sn_lv_mva\",\"vn_hv_kv\",\"vn_mv_kv\",\"vn_lv_kv\",\"vk_hv_percent\",\"vk_mv_percent\",\"vk_lv_percent\",\"vkr_hv_percent\",\"vkr_mv_percent\",\"vkr_lv_percent\",\"pfe_kw\",\"i0_percent\",\"shift_mv_degree\",\"shift_lv_degree\",\"tap_side\",\"tap_neutral\",\"tap_min\",\"tap_max\",\"tap_step_percent\",\"tap_step_degree\",\"tap_pos\",\"tap_at_star_point\",\"in_service\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "std_type": "object", "hv_bus": "uint32", "mv_bus": "uint32", "lv_bus": "uint32", "sn_hv_mva": "float64", "sn_mv_mva": "float64", "sn_lv_mva": "float64", "vn_hv_kv": "float64", "vn_mv_kv": "float64", "vn_lv_kv": "float64", "vk_hv_percent": "float64", "vk_mv_percent": "float64", "vk_lv_percent": "float64", "vkr_hv_percent": "float64", "vkr_mv_percent": "float64", "vkr_lv_percent": "float64", "pfe_kw": "float64", "i0_percent": "float64", "shift_mv_degree": "float64", "shift_lv_degree": "float64", "tap_side": "object", "tap_neutral": "int32", "tap_min": "int32", "tap_max": "int32", "tap_step_percent": "float64", "tap_step_degree": "float64", "tap_pos": "int32", "tap_at_star_point": "bool", "in_service": "bool"}}, "impedance": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"from_bus\",\"to_bus\",\"rft_pu\",\"xft_pu\",\"rtf_pu\",\"xtf_pu\",\"sn_mva\",\"in_service\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "from_bus": "uint32", "to_bus": "uint32", "rft_pu": "float64", "xft_pu": "float64", "rtf_pu": "float64", "xtf_pu": "float64", "sn_mva": "float64", "in_service": "bool"}}, "dcline": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"from_bus\",\"to_bus\",\"p_mw\",\"loss_percent\",\"loss_mw\",\"vm_from_pu\",\"vm_to_pu\",\"max_p_mw\",\"min_q_from_mvar\",\"min_q_to_mvar\",\"max_q_from_mvar\",\"max_q_to_mvar\",\"in_service\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "from_bus": "uint32", "to_bus": "uint32", "p_mw": "float64", "loss_percent": "float64", "loss_mw": "float64", "vm_from_pu": "float64", "vm_to_pu": "float64", "max_p_mw": "float64", "min_q_from_mvar": "float64", "min_q_to_mvar": "float64", "max_q_from_mvar": "float64", "max_q_to_mvar": "float64", "in_service": "bool"}}, "ward": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"ps_mw\",\"qs_mvar\",\"qz_mvar\",\"pz_mw\",\"in_service\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "bus": "uint32", "ps_mw": "float64", "qs_mvar": "float64", "qz_mvar": "float64", "pz_mw": "float64", "in_service": "bool"}}, "xward": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"bus\",\"ps_mw\",\"qs_mvar\",\"qz_mvar\",\"pz_mw\",\"r_ohm\",\"x_ohm\",\"vm_pu\",\"in_service\",\"slack_weight\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "bus": "uint32", "ps_mw": "float64", "qs_mvar": "float64", "qz_mvar": "float64", "pz_mw": "float64", "r_ohm": "float64", "x_ohm": "float64", "vm_pu": "float64", "in_service": "bool", "slack_weight": "float64"}}, "measurement": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"name\",\"measurement_type\",\"element_type\",\"element\",\"value\",\"std_dev\",\"side\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"name": "object", "measurement_type": "object", "element_type": "object", "element": "uint32", "value": "float64", "std_dev": "float64", "side": "object"}}, "pwl_cost": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"power_type\",\"element\",\"et\",\"points\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"power_type": "object", "element": "uint32", "et": "object", "points": "object"}}, "poly_cost": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"element\",\"et\",\"cp0_eur\",\"cp1_eur_per_mw\",\"cp2_eur_per_mw2\",\"cq0_eur\",\"cq1_eur_per_mvar\",\"cq2_eur_per_mvar2\"],\"index\":[0,1,2,3,4],\"data\":[[0,\"ext_grid\",0.0,20.0,0.0430293,0.0,0.0,0.0],[0,\"gen\",0.0,20.0,0.25,0.0,0.0,0.0],[1,\"gen\",0.0,40.0,0.01,0.0,0.0,0.0],[2,\"gen\",0.0,40.0,0.01,0.0,0.0,0.0],[3,\"gen\",0.0,40.0,0.01,0.0,0.0,0.0]]}", "orient": "split", "dtype": {"element": "uint32", "et": "object", "cp0_eur": "float64", "cp1_eur_per_mw": "float64", "cp2_eur_per_mw2": "float64", "cq0_eur": "float64", "cq1_eur_per_mvar": "float64", "cq2_eur_per_mvar2": "float64"}}, "characteristic": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"object\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"object": "object"}}, "controller": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"object\",\"in_service\",\"order\",\"level\",\"initial_run\",\"recycle\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"object": "object", "in_service": "bool", "order": "float64", "level": "object", "initial_run": "bool", "recycle": "object"}}, "line_geodata": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"coords\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"coords": "object"}}, "bus_geodata": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"x\",\"y\",\"coords\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"x": "float64", "y": "float64", "coords": "object"}}, "version": "2.8.0", "converged": false, "name": "", "f_hz": 50, "sn_mva": 1.0, "std_types": {"line": {"NAYY 4x50 SE": {"c_nf_per_km": 210, "r_ohm_per_km": 0.642, "x_ohm_per_km": 0.083, "max_i_ka": 0.142, "type": "cs", "q_mm2": 50, "alpha": 0.00403}, "NAYY 4x120 SE": {"c_nf_per_km": 264, "r_ohm_per_km": 0.225, "x_ohm_per_km": 0.08, "max_i_ka": 0.242, "type": "cs", "q_mm2": 120, "alpha": 0.00403}, "NAYY 4x150 SE": {"c_nf_per_km": 261, "r_ohm_per_km": 0.208, "x_ohm_per_km": 0.08, "max_i_ka": 0.27, "type": "cs", "q_mm2": 150, "alpha": 0.00403}, "NA2XS2Y 1x95 RM/25 12/20 kV": {"c_nf_per_km": 216, "r_ohm_per_km": 0.313, "x_ohm_per_km": 0.132, "max_i_ka": 0.252, "type": "cs", "q_mm2": 95, "alpha": 0.00403}, "NA2XS2Y 1x185 RM/25 12/20 kV": {"c_nf_per_km": 273, "r_ohm_per_km": 0.161, "x_ohm_per_km": 0.117, "max_i_ka": 0.362, "type": "cs", "q_mm2": 185, "alpha": 0.00403}, "NA2XS2Y 1x240 RM/25 12/20 kV": {"c_nf_per_km": 304, "r_ohm_per_km": 0.122, "x_ohm_per_km": 0.112, "max_i_ka": 0.421, "type": "cs", "q_mm2": 240, "alpha": 0.00403}, "NA2XS2Y 1x95 RM/25 6/10 kV": {"c_nf_per_km": 315, "r_ohm_per_km": 0.313, "x_ohm_per_km": 0.123, "max_i_ka": 0.249, "type": "cs", "q_mm2": 95, "alpha": 0.00403}, "NA2XS2Y 1x185 RM/25 6/10 kV": {"c_nf_per_km": 406, "r_ohm_per_km": 0.161, "x_ohm_per_km": 0.11, "max_i_ka": 0.358, "type": "cs", "q_mm2": 185, "alpha": 0.00403}, "NA2XS2Y 1x240 RM/25 6/10 kV": {"c_nf_per_km": 456, "r_ohm_per_km": 0.122, "x_ohm_per_km": 0.105, "max_i_ka": 0.416, "type": "cs", "q_mm2": 240, "alpha": 0.00403}, "NA2XS2Y 1x150 RM/25 12/20 kV": {"c_nf_per_km": 250, "r_ohm_per_km": 0.206, "x_ohm_per_km": 0.116, "max_i_ka": 0.319, "type": "cs", "q_mm2": 150, "alpha": 0.00403}, "NA2XS2Y 1x120 RM/25 12/20 kV": {"c_nf_per_km": 230, "r_ohm_per_km": 0.253, "x_ohm_per_km": 0.119, "max_i_ka": 0.283, "type": "cs", "q_mm2": 120, "alpha": 0.00403}, "NA2XS2Y 1x70 RM/25 12/20 kV": {"c_nf_per_km": 190, "r_ohm_per_km": 0.443, "x_ohm_per_km": 0.132, "max_i_ka": 0.22, "type": "cs", "q_mm2": 70, "alpha": 0.00403}, "NA2XS2Y 1x150 RM/25 6/10 kV": {"c_nf_per_km": 360, "r_ohm_per_km": 0.206, "x_ohm_per_km": 0.11, "max_i_ka": 0.315, "type": "cs", "q_mm2": 150, "alpha": 0.00403}, "NA2XS2Y 1x120 RM/25 6/10 kV": {"c_nf_per_km": 340, "r_ohm_per_km": 0.253, "x_ohm_per_km": 0.113, "max_i_ka": 0.28, "type": "cs", "q_mm2": 120, "alpha": 0.00403}, "NA2XS2Y 1x70 RM/25 6/10 kV": {"c_nf_per_km": 280, "r_ohm_per_km": 0.443, "x_ohm_per_km": 0.123, "max_i_ka": 0.217, "type": "cs", "q_mm2": 70, "alpha": 0.00403}, "N2XS(FL)2Y 1x120 RM/35 64/110 kV": {"c_nf_per_km": 112, "r_ohm_per_km": 0.153, "x_ohm_per_km": 0.166, "max_i_ka": 0.366, "type": "cs", "q_mm2": 120, "alpha": 0.00393}, "N2XS(FL)2Y 1x185 RM/35 64/110 kV": {"c_nf_per_km": 125, "r_ohm_per_km": 0.099, "x_ohm_per_km": 0.156, "max_i_ka": 0.457, "type": "cs", "q_mm2": 185, "alpha": 0.00393}, "N2XS(FL)2Y 1x240 RM/35 64/110 kV": {"c_nf_per_km": 135, "r_ohm_per_km": 0.075, "x_ohm_per_km": 0.149, "max_i_ka": 0.526, "type": "cs", "q_mm2": 240, "alpha": 0.00393}, "N2XS(FL)2Y 1x300 RM/35 64/110 kV": {"c_nf_per_km": 144, "r_ohm_per_km": 0.06, "x_ohm_per_km": 0.144, "max_i_ka": 0.588, "type": "cs", "q_mm2": 300, "alpha": 0.00393}, "15-AL1/3-ST1A 0.4": {"c_nf_per_km": 11, "r_ohm_per_km": 1.8769, "x_ohm_per_km": 0.35, "max_i_ka": 0.105, "type": "ol", "q_mm2": 16, "alpha": 0.00403}, "24-AL1/4-ST1A 0.4": {"c_nf_per_km": 11.25, "r_ohm_per_km": 1.2012, "x_ohm_per_km": 0.335, "max_i_ka": 0.14, "type": "ol", "q_mm2": 24, "alpha": 0.00403}, "48-AL1/8-ST1A 0.4": {"c_nf_per_km": 12.2, "r_ohm_per_km": 0.5939, "x_ohm_per_km": 0.3, "max_i_ka": 0.21, "type": "ol", "q_mm2": 48, "alpha": 0.00403}, "94-AL1/15-ST1A 0.4": {"c_nf_per_km": 13.2, "r_ohm_per_km": 0.306, "x_ohm_per_km": 0.29, "max_i_ka": 0.35, "type": "ol", "q_mm2": 94, "alpha": 0.00403}, "34-AL1/6-ST1A 10.0": {"c_nf_per_km": 9.7, "r_ohm_per_km": 0.8342, "x_ohm_per_km": 0.36, "max_i_ka": 0.17, "type": "ol", "q_mm2": 34, "alpha": 0.00403}, "48-AL1/8-ST1A 10.0": {"c_nf_per_km": 10.1, "r_ohm_per_km": 0.5939, "x_ohm_per_km": 0.35, "max_i_ka": 0.21, "type": "ol", "q_mm2": 48, "alpha": 0.00403}, "70-AL1/11-ST1A 10.0": {"c_nf_per_km": 10.4, "r_ohm_per_km": 0.4132, "x_ohm_per_km": 0.339, "max_i_ka": 0.29, "type": "ol", "q_mm2": 70, "alpha": 0.00403}, "94-AL1/15-ST1A 10.0": {"c_nf_per_km": 10.75, "r_ohm_per_km": 0.306, "x_ohm_per_km": 0.33, "max_i_ka": 0.35, "type": "ol", "q_mm2": 94, "alpha": 0.00403}, "122-AL1/20-ST1A 10.0": {"c_nf_per_km": 11.1, "r_ohm_per_km": 0.2376, "x_ohm_per_km": 0.323, "max_i_ka": 0.41, "type": "ol", "q_mm2": 122, "alpha": 0.00403}, "149-AL1/24-ST1A 10.0": {"c_nf_per_km": 11.25, "r_ohm_per_km": 0.194, "x_ohm_per_km": 0.315, "max_i_ka": 0.47, "type": "ol", "q_mm2": 149, "alpha": 0.00403}, "34-AL1/6-ST1A 20.0": {"c_nf_per_km": 9.15, "r_ohm_per_km": 0.8342, "x_ohm_per_km": 0.382, "max_i_ka": 0.17, "type": "ol", "q_mm2": 34, "alpha": 0.00403}, "48-AL1/8-ST1A 20.0": {"c_nf_per_km": 9.5, "r_ohm_per_km": 0.5939, "x_ohm_per_km": 0.372, "max_i_ka": 0.21, "type": "ol", "q_mm2": 48, "alpha": 0.00403}, "70-AL1/11-ST1A 20.0": {"c_nf_per_km": 9.7, "r_ohm_per_km": 0.4132, "x_ohm_per_km": 0.36, "max_i_ka": 0.29, "type": "ol", "q_mm2": 70, "alpha": 0.00403}, "94-AL1/15-ST1A 20.0": {"c_nf_per_km": 10, "r_ohm_per_km": 0.306, "x_ohm_per_km": 0.35, "max_i_ka": 0.35, "type": "ol", "q_mm2": 94, "alpha": 0.00403}, "122-AL1/20-ST1A 20.0": {"c_nf_per_km": 10.3, "r_ohm_per_km": 0.2376, "x_ohm_per_km": 0.344, "max_i_ka": 0.41, "type": "ol", "q_mm2": 122, "alpha": 0.00403}, "149-AL1/24-ST1A 20.0": {"c_nf_per_km": 10.5, "r_ohm_per_km": 0.194, "x_ohm_per_km": 0.337, "max_i_ka": 0.47, "type": "ol", "q_mm2": 149, "alpha": 0.00403}, "184-AL1/30-ST1A 20.0": {"c_nf_per_km": 10.75, "r_ohm_per_km": 0.1571, "x_ohm_per_km": 0.33, "max_i_ka": 0.535, "type": "ol", "q_mm2": 184, "alpha": 0.00403}, "243-AL1/39-ST1A 20.0": {"c_nf_per_km": 11, "r_ohm_per_km": 0.1188, "x_ohm_per_km": 0.32, "max_i_ka": 0.645, "type": "ol", "q_mm2": 243, "alpha": 0.00403}, "48-AL1/8-ST1A 110.0": {"c_nf_per_km": 8, "r_ohm_per_km": 0.5939, "x_ohm_per_km": 0.46, "max_i_ka": 0.21, "type": "ol", "q_mm2": 48, "alpha": 0.00403}, "70-AL1/11-ST1A 110.0": {"c_nf_per_km": 8.4, "r_ohm_per_km": 0.4132, "x_ohm_per_km": 0.45, "max_i_ka": 0.29, "type": "ol", "q_mm2": 70, "alpha": 0.00403}, "94-AL1/15-ST1A 110.0": {"c_nf_per_km": 8.65, "r_ohm_per_km": 0.306, "x_ohm_per_km": 0.44, "max_i_ka": 0.35, "type": "ol", "q_mm2": 94, "alpha": 0.00403}, "122-AL1/20-ST1A 110.0": {"c_nf_per_km": 8.5, "r_ohm_per_km": 0.2376, "x_ohm_per_km": 0.43, "max_i_ka": 0.41, "type": "ol", "q_mm2": 122, "alpha": 0.00403}, "149-AL1/24-ST1A 110.0": {"c_nf_per_km": 8.75, "r_ohm_per_km": 0.194, "x_ohm_per_km": 0.41, "max_i_ka": 0.47, "type": "ol", "q_mm2": 149, "alpha": 0.00403}, "184-AL1/30-ST1A 110.0": {"c_nf_per_km": 8.8, "r_ohm_per_km": 0.1571, "x_ohm_per_km": 0.4, "max_i_ka": 0.535, "type": "ol", "q_mm2": 184, "alpha": 0.00403}, "243-AL1/39-ST1A 110.0": {"c_nf_per_km": 9, "r_ohm_per_km": 0.1188, "x_ohm_per_km": 0.39, "max_i_ka": 0.645, "type": "ol", "q_mm2": 243, "alpha": 0.00403}, "305-AL1/39-ST1A 110.0": {"c_nf_per_km": 9.2, "r_ohm_per_km": 0.0949, "x_ohm_per_km": 0.38, "max_i_ka": 0.74, "type": "ol", "q_mm2": 305, "alpha": 0.00403}, "490-AL1/64-ST1A 110.0": {"c_nf_per_km": 9.75, "r_ohm_per_km": 0.059, "x_ohm_per_km": 0.37, "max_i_ka": 0.96, "type": "ol", "q_mm2": 490, "alpha": 0.00403}, "679-AL1/86-ST1A 110.0": {"c_nf_per_km": 9.95, "r_ohm_per_km": 0.042, "x_ohm_per_km": 0.36, "max_i_ka": 1.15, "type": "ol", "q_mm2": 679, "alpha": 0.00403}, "490-AL1/64-ST1A 220.0": {"c_nf_per_km": 10, "r_ohm_per_km": 0.059, "x_ohm_per_km": 0.285, "max_i_ka": 0.96, "type": "ol", "q_mm2": 490, "alpha": 0.00403}, "679-AL1/86-ST1A 220.0": {"c_nf_per_km": 11.7, "r_ohm_per_km": 0.042, "x_ohm_per_km": 0.275, "max_i_ka": 1.15, "type": "ol", "q_mm2": 679, "alpha": 0.00403}, "490-AL1/64-ST1A 380.0": {"c_nf_per_km": 11, "r_ohm_per_km": 0.059, "x_ohm_per_km": 0.253, "max_i_ka": 0.96, "type": "ol", "q_mm2": 490, "alpha": 0.00403}, "679-AL1/86-ST1A 380.0": {"c_nf_per_km": 14.6, "r_ohm_per_km": 0.042, "x_ohm_per_km": 0.25, "max_i_ka": 1.15, "type": "ol", "q_mm2": 679, "alpha": 0.00403}}, "trafo": {"160 MVA 380/110 kV": {"i0_percent": 0.06, "pfe_kw": 60, "vkr_percent": 0.25, "sn_mva": 160, "vn_lv_kv": 110.0, "vn_hv_kv": 380.0, "vk_percent": 12.2, "shift_degree": 0, "vector_group": "Yy0", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "100 MVA 220/110 kV": {"i0_percent": 0.06, "pfe_kw": 55, "vkr_percent": 0.26, "sn_mva": 100, "vn_lv_kv": 110.0, "vn_hv_kv": 220.0, "vk_percent": 12.0, "shift_degree": 0, "vector_group": "Yy0", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "63 MVA 110/20 kV": {"i0_percent": 0.04, "pfe_kw": 22, "vkr_percent": 0.32, "sn_mva": 63, "vn_lv_kv": 20.0, "vn_hv_kv": 110.0, "vk_percent": 18, "shift_degree": 150, "vector_group": "YNd5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "40 MVA 110/20 kV": {"i0_percent": 0.05, "pfe_kw": 18, "vkr_percent": 0.34, "sn_mva": 40, "vn_lv_kv": 20.0, "vn_hv_kv": 110.0, "vk_percent": 16.2, "shift_degree": 150, "vector_group": "YNd5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "25 MVA 110/20 kV": {"i0_percent": 0.07, "pfe_kw": 14, "vkr_percent": 0.41, "sn_mva": 25, "vn_lv_kv": 20.0, "vn_hv_kv": 110.0, "vk_percent": 12, "shift_degree": 150, "vector_group": "YNd5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "63 MVA 110/10 kV": {"sn_mva": 63, "vn_hv_kv": 110, "vn_lv_kv": 10, "vk_percent": 18, "vkr_percent": 0.32, "pfe_kw": 22, "i0_percent": 0.04, "shift_degree": 150, "vector_group": "YNd5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "40 MVA 110/10 kV": {"sn_mva": 40, "vn_hv_kv": 110, "vn_lv_kv": 10, "vk_percent": 16.2, "vkr_percent": 0.34, "pfe_kw": 18, "i0_percent": 0.05, "shift_degree": 150, "vector_group": "YNd5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "25 MVA 110/10 kV": {"sn_mva": 25, "vn_hv_kv": 110, "vn_lv_kv": 10, "vk_percent": 12, "vkr_percent": 0.41, "pfe_kw": 14, "i0_percent": 0.07, "shift_degree": 150, "vector_group": "YNd5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -9, "tap_max": 9, "tap_step_degree": 0, "tap_step_percent": 1.5, "tap_phase_shifter": false}, "0.25 MVA 20/0.4 kV": {"sn_mva": 0.25, "vn_hv_kv": 20, "vn_lv_kv": 0.4, "vk_percent": 6, "vkr_percent": 1.44, "pfe_kw": 0.8, "i0_percent": 0.32, "shift_degree": 150, "vector_group": "Yzn5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -2, "tap_max": 2, "tap_step_degree": 0, "tap_step_percent": 2.5, "tap_phase_shifter": false}, "0.4 MVA 20/0.4 kV": {"sn_mva": 0.4, "vn_hv_kv": 20, "vn_lv_kv": 0.4, "vk_percent": 6, "vkr_percent": 1.425, "pfe_kw": 1.35, "i0_percent": 0.3375, "shift_degree": 150, "vector_group": "Dyn5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -2, "tap_max": 2, "tap_step_degree": 0, "tap_step_percent": 2.5, "tap_phase_shifter": false}, "0.63 MVA 20/0.4 kV": {"sn_mva": 0.63, "vn_hv_kv": 20, "vn_lv_kv": 0.4, "vk_percent": 6, "vkr_percent": 1.206, "pfe_kw": 1.65, "i0_percent": 0.2619, "shift_degree": 150, "vector_group": "Dyn5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -2, "tap_max": 2, "tap_step_degree": 0, "tap_step_percent": 2.5, "tap_phase_shifter": false}, "0.25 MVA 10/0.4 kV": {"sn_mva": 0.25, "vn_hv_kv": 10, "vn_lv_kv": 0.4, "vk_percent": 4, "vkr_percent": 1.2, "pfe_kw": 0.6, "i0_percent": 0.24, "shift_degree": 150, "vector_group": "Dyn5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -2, "tap_max": 2, "tap_step_degree": 0, "tap_step_percent": 2.5, "tap_phase_shifter": false}, "0.4 MVA 10/0.4 kV": {"sn_mva": 0.4, "vn_hv_kv": 10, "vn_lv_kv": 0.4, "vk_percent": 4, "vkr_percent": 1.325, "pfe_kw": 0.95, "i0_percent": 0.2375, "shift_degree": 150, "vector_group": "Dyn5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -2, "tap_max": 2, "tap_step_degree": 0, "tap_step_percent": 2.5, "tap_phase_shifter": false}, "0.63 MVA 10/0.4 kV": {"sn_mva": 0.63, "vn_hv_kv": 10, "vn_lv_kv": 0.4, "vk_percent": 4, "vkr_percent": 1.0794, "pfe_kw": 1.18, "i0_percent": 0.1873, "shift_degree": 150, "vector_group": "Dyn5", "tap_side": "hv", "tap_neutral": 0, "tap_min": -2, "tap_max": 2, "tap_step_degree": 0, "tap_step_percent": 2.5, "tap_phase_shifter": false}}, "trafo3w": {"63/25/38 MVA 110/20/10 kV": {"sn_hv_mva": 63, "sn_mv_mva": 25, "sn_lv_mva": 38, "vn_hv_kv": 110, "vn_mv_kv": 20, "vn_lv_kv": 10, "vk_hv_percent": 10.4, "vk_mv_percent": 10.4, "vk_lv_percent": 10.4, "vkr_hv_percent": 0.28, "vkr_mv_percent": 0.32, "vkr_lv_percent": 0.35, "pfe_kw": 35, "i0_percent": 0.89, "shift_mv_degree": 0, "shift_lv_degree": 0, "vector_group": "YN0yn0yn0", "tap_side": "hv", "tap_neutral": 0, "tap_min": -10, "tap_max": 10, "tap_step_percent": 1.2}, "63/25/38 MVA 110/10/10 kV": {"sn_hv_mva": 63, "sn_mv_mva": 25, "sn_lv_mva": 38, "vn_hv_kv": 110, "vn_mv_kv": 10, "vn_lv_kv": 10, "vk_hv_percent": 10.4, "vk_mv_percent": 10.4, "vk_lv_percent": 10.4, "vkr_hv_percent": 0.28, "vkr_mv_percent": 0.32, "vkr_lv_percent": 0.35, "pfe_kw": 35, "i0_percent": 0.89, "shift_mv_degree": 0, "shift_lv_degree": 0, "vector_group": "YN0yn0yn0", "tap_side": "hv", "tap_neutral": 0, "tap_min": -10, "tap_max": 10, "tap_step_percent": 1.2}}}, "res_bus": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"vm_pu\",\"va_degree\",\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"vm_pu": "float64", "va_degree": "float64", "p_mw": "float64", "q_mvar": "float64"}}, "res_line": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_from_mw\",\"q_from_mvar\",\"p_to_mw\",\"q_to_mvar\",\"pl_mw\",\"ql_mvar\",\"i_from_ka\",\"i_to_ka\",\"i_ka\",\"vm_from_pu\",\"va_from_degree\",\"vm_to_pu\",\"va_to_degree\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_from_mw": "float64", "q_from_mvar": "float64", "p_to_mw": "float64", "q_to_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_from_ka": "float64", "i_to_ka": "float64", "i_ka": "float64", "vm_from_pu": "float64", "va_from_degree": "float64", "vm_to_pu": "float64", "va_to_degree": "float64", "loading_percent": "float64"}}, "res_trafo": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_hv_mw\",\"q_hv_mvar\",\"p_lv_mw\",\"q_lv_mvar\",\"pl_mw\",\"ql_mvar\",\"i_hv_ka\",\"i_lv_ka\",\"vm_hv_pu\",\"va_hv_degree\",\"vm_lv_pu\",\"va_lv_degree\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_hv_mw": "float64", "q_hv_mvar": "float64", "p_lv_mw": "float64", "q_lv_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_hv_ka": "float64", "i_lv_ka": "float64", "vm_hv_pu": "float64", "va_hv_degree": "float64", "vm_lv_pu": "float64", "va_lv_degree": "float64", "loading_percent": "float64"}}, "res_trafo3w": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_hv_mw\",\"q_hv_mvar\",\"p_mv_mw\",\"q_mv_mvar\",\"p_lv_mw\",\"q_lv_mvar\",\"pl_mw\",\"ql_mvar\",\"i_hv_ka\",\"i_mv_ka\",\"i_lv_ka\",\"vm_hv_pu\",\"va_hv_degree\",\"vm_mv_pu\",\"va_mv_degree\",\"vm_lv_pu\",\"va_lv_degree\",\"va_internal_degree\",\"vm_internal_pu\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_hv_mw": "float64", "q_hv_mvar": "float64", "p_mv_mw": "float64", "q_mv_mvar": "float64", "p_lv_mw": "float64", "q_lv_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_hv_ka": "float64", "i_mv_ka": "float64", "i_lv_ka": "float64", "vm_hv_pu": "float64", "va_hv_degree": "float64", "vm_mv_pu": "float64", "va_mv_degree": "float64", "vm_lv_pu": "float64", "va_lv_degree": "float64", "va_internal_degree": "float64", "vm_internal_pu": "float64", "loading_percent": "float64"}}, "res_impedance": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_from_mw\",\"q_from_mvar\",\"p_to_mw\",\"q_to_mvar\",\"pl_mw\",\"ql_mvar\",\"i_from_ka\",\"i_to_ka\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_from_mw": "float64", "q_from_mvar": "float64", "p_to_mw": "float64", "q_to_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_from_ka": "float64", "i_to_ka": "float64"}}, "res_ext_grid": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_load": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_motor": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_sgen": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_storage": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_shunt": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\",\"vm_pu\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64", "vm_pu": "float64"}}, "res_gen": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\",\"va_degree\",\"vm_pu\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64", "va_degree": "float64", "vm_pu": "float64"}}, "res_ward": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\",\"vm_pu\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64", "vm_pu": "float64"}}, "res_xward": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\",\"vm_pu\",\"va_internal_degree\",\"vm_internal_pu\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64", "vm_pu": "float64", "va_internal_degree": "float64", "vm_internal_pu": "float64"}}, "res_dcline": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_from_mw\",\"q_from_mvar\",\"p_to_mw\",\"q_to_mvar\",\"pl_mw\",\"vm_from_pu\",\"va_from_degree\",\"vm_to_pu\",\"va_to_degree\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_from_mw": "float64", "q_from_mvar": "float64", "p_to_mw": "float64", "q_to_mvar": "float64", "pl_mw": "float64", "vm_from_pu": "float64", "va_from_degree": "float64", "vm_to_pu": "float64", "va_to_degree": "float64"}}, "res_asymmetric_load": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_asymmetric_sgen": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_bus_est": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"vm_pu\",\"va_degree\",\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"vm_pu": "float64", "va_degree": "float64", "p_mw": "float64", "q_mvar": "float64"}}, "res_line_est": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_from_mw\",\"q_from_mvar\",\"p_to_mw\",\"q_to_mvar\",\"pl_mw\",\"ql_mvar\",\"i_from_ka\",\"i_to_ka\",\"i_ka\",\"vm_from_pu\",\"va_from_degree\",\"vm_to_pu\",\"va_to_degree\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_from_mw": "float64", "q_from_mvar": "float64", "p_to_mw": "float64", "q_to_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_from_ka": "float64", "i_to_ka": "float64", "i_ka": "float64", "vm_from_pu": "float64", "va_from_degree": "float64", "vm_to_pu": "float64", "va_to_degree": "float64", "loading_percent": "float64"}}, "res_trafo_est": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_hv_mw\",\"q_hv_mvar\",\"p_lv_mw\",\"q_lv_mvar\",\"pl_mw\",\"ql_mvar\",\"i_hv_ka\",\"i_lv_ka\",\"vm_hv_pu\",\"va_hv_degree\",\"vm_lv_pu\",\"va_lv_degree\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_hv_mw": "float64", "q_hv_mvar": "float64", "p_lv_mw": "float64", "q_lv_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_hv_ka": "float64", "i_lv_ka": "float64", "vm_hv_pu": "float64", "va_hv_degree": "float64", "vm_lv_pu": "float64", "va_lv_degree": "float64", "loading_percent": "float64"}}, "res_trafo3w_est": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_hv_mw\",\"q_hv_mvar\",\"p_mv_mw\",\"q_mv_mvar\",\"p_lv_mw\",\"q_lv_mvar\",\"pl_mw\",\"ql_mvar\",\"i_hv_ka\",\"i_mv_ka\",\"i_lv_ka\",\"vm_hv_pu\",\"va_hv_degree\",\"vm_mv_pu\",\"va_mv_degree\",\"vm_lv_pu\",\"va_lv_degree\",\"va_internal_degree\",\"vm_internal_pu\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_hv_mw": "float64", "q_hv_mvar": "float64", "p_mv_mw": "float64", "q_mv_mvar": "float64", "p_lv_mw": "float64", "q_lv_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_hv_ka": "float64", "i_mv_ka": "float64", "i_lv_ka": "float64", "vm_hv_pu": "float64", "va_hv_degree": "float64", "vm_mv_pu": "float64", "va_mv_degree": "float64", "vm_lv_pu": "float64", "va_lv_degree": "float64", "va_internal_degree": "float64", "vm_internal_pu": "float64", "loading_percent": "float64"}}, "res_impedance_est": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_from_mw\",\"q_from_mvar\",\"p_to_mw\",\"q_to_mvar\",\"pl_mw\",\"ql_mvar\",\"i_from_ka\",\"i_to_ka\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_from_mw": "float64", "q_from_mvar": "float64", "p_to_mw": "float64", "q_to_mvar": "float64", "pl_mw": "float64", "ql_mvar": "float64", "i_from_ka": "float64", "i_to_ka": "float64"}}, "res_bus_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_line_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_trafo_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_trafo3w_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_ext_grid_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_gen_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_sgen_sc": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_bus_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"vm_a_pu\",\"va_a_degree\",\"vm_b_pu\",\"va_b_degree\",\"vm_c_pu\",\"va_c_degree\",\"p_a_mw\",\"q_a_mvar\",\"p_b_mw\",\"q_b_mvar\",\"p_c_mw\",\"q_c_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"vm_a_pu": "float64", "va_a_degree": "float64", "vm_b_pu": "float64", "va_b_degree": "float64", "vm_c_pu": "float64", "va_c_degree": "float64", "p_a_mw": "float64", "q_a_mvar": "float64", "p_b_mw": "float64", "q_b_mvar": "float64", "p_c_mw": "float64", "q_c_mvar": "float64"}}, "res_line_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_a_from_mw\",\"q_a_from_mvar\",\"p_b_from_mw\",\"q_b_from_mvar\",\"q_c_from_mvar\",\"p_a_to_mw\",\"q_a_to_mvar\",\"p_b_to_mw\",\"q_b_to_mvar\",\"p_c_to_mw\",\"q_c_to_mvar\",\"p_a_l_mw\",\"q_a_l_mvar\",\"p_b_l_mw\",\"q_b_l_mvar\",\"p_c_l_mw\",\"q_c_l_mvar\",\"i_a_from_ka\",\"i_a_to_ka\",\"i_b_from_ka\",\"i_b_to_ka\",\"i_c_from_ka\",\"i_c_to_ka\",\"i_a_ka\",\"i_b_ka\",\"i_c_ka\",\"i_n_from_ka\",\"i_n_to_ka\",\"i_n_ka\",\"loading_a_percent\",\"loading_b_percent\",\"loading_c_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_a_from_mw": "float64", "q_a_from_mvar": "float64", "p_b_from_mw": "float64", "q_b_from_mvar": "float64", "q_c_from_mvar": "float64", "p_a_to_mw": "float64", "q_a_to_mvar": "float64", "p_b_to_mw": "float64", "q_b_to_mvar": "float64", "p_c_to_mw": "float64", "q_c_to_mvar": "float64", "p_a_l_mw": "float64", "q_a_l_mvar": "float64", "p_b_l_mw": "float64", "q_b_l_mvar": "float64", "p_c_l_mw": "float64", "q_c_l_mvar": "float64", "i_a_from_ka": "float64", "i_a_to_ka": "float64", "i_b_from_ka": "float64", "i_b_to_ka": "float64", "i_c_from_ka": "float64", "i_c_to_ka": "float64", "i_a_ka": "float64", "i_b_ka": "float64", "i_c_ka": "float64", "i_n_from_ka": "float64", "i_n_to_ka": "float64", "i_n_ka": "float64", "loading_a_percent": "float64", "loading_b_percent": "float64", "loading_c_percent": "float64"}}, "res_trafo_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_a_hv_mw\",\"q_a_hv_mvar\",\"p_b_hv_mw\",\"q_b_hv_mvar\",\"p_c_hv_mw\",\"q_c_hv_mvar\",\"p_a_lv_mw\",\"q_a_lv_mvar\",\"p_b_lv_mw\",\"q_b_lv_mvar\",\"p_c_lv_mw\",\"q_c_lv_mvar\",\"p_a_l_mw\",\"q_a_l_mvar\",\"p_b_l_mw\",\"q_b_l_mvar\",\"p_c_l_mw\",\"q_c_l_mvar\",\"i_a_hv_ka\",\"i_a_lv_ka\",\"i_b_hv_ka\",\"i_b_lv_ka\",\"i_c_hv_ka\",\"i_c_lv_ka\",\"loading_a_percent\",\"loading_b_percent\",\"loading_c_percent\",\"loading_percent\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_a_hv_mw": "float64", "q_a_hv_mvar": "float64", "p_b_hv_mw": "float64", "q_b_hv_mvar": "float64", "p_c_hv_mw": "float64", "q_c_hv_mvar": "float64", "p_a_lv_mw": "float64", "q_a_lv_mvar": "float64", "p_b_lv_mw": "float64", "q_b_lv_mvar": "float64", "p_c_lv_mw": "float64", "q_c_lv_mvar": "float64", "p_a_l_mw": "float64", "q_a_l_mvar": "float64", "p_b_l_mw": "float64", "q_b_l_mvar": "float64", "p_c_l_mw": "float64", "q_c_l_mvar": "float64", "i_a_hv_ka": "float64", "i_a_lv_ka": "float64", "i_b_hv_ka": "float64", "i_b_lv_ka": "float64", "i_c_hv_ka": "float64", "i_c_lv_ka": "float64", "loading_a_percent": "float64", "loading_b_percent": "float64", "loading_c_percent": "float64", "loading_percent": "float64"}}, "res_ext_grid_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_a_mw\",\"q_a_mvar\",\"p_b_mw\",\"q_b_mvar\",\"p_c_mw\",\"q_c_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_a_mw": "float64", "q_a_mvar": "float64", "p_b_mw": "float64", "q_b_mvar": "float64", "p_c_mw": "float64", "q_c_mvar": "float64"}}, "res_shunt_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[],\"index\":[],\"data\":[]}", "orient": "split"}, "res_load_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_sgen_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_storage_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_mw\",\"q_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_mw": "float64", "q_mvar": "float64"}}, "res_asymmetric_load_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_a_mw\",\"q_a_mvar\",\"p_b_mw\",\"q_b_mvar\",\"p_c_mw\",\"q_c_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_a_mw": "float64", "q_a_mvar": "float64", "p_b_mw": "float64", "q_b_mvar": "float64", "p_c_mw": "float64", "q_c_mvar": "float64"}}, "res_asymmetric_sgen_3ph": {"_module": "pandas.core.frame", "_class": "DataFrame", "_object": "{\"columns\":[\"p_a_mw\",\"q_a_mvar\",\"p_b_mw\",\"q_b_mvar\",\"p_c_mw\",\"q_c_mvar\"],\"index\":[],\"data\":[]}", "orient": "split", "dtype": {"p_a_mw": "float64", "q_a_mvar": "float64", "p_b_mw": "float64", "q_b_mvar": "float64", "p_c_mw": "float64", "q_c_mvar": "float64"}}, "user_pf_options": {}}}