torchvision-0.16.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.16.2.dist-info/LICENSE,sha256=ZQL2doUc_iX4r3VTHfsyN1tzJbc8N-e0N0H6QiiT5x0,1517
torchvision-0.16.2.dist-info/METADATA,sha256=JPKQ6dT3rDLcO8BBZtxUpCIf6xAoPBpiwcpvbrQvMpY,6571
torchvision-0.16.2.dist-info/RECORD,,
torchvision-0.16.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torchvision-0.16.2.dist-info/WHEEL,sha256=u895uB4FIjm1puccTaPTl57dorjncEYygQxItKHr_T0,108
torchvision-0.16.2.dist-info/top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision/.dylibs/libc++.1.0.dylib,sha256=ZPUjO0cLT87kSktCntVtPfrmjwzHzYbb3NFdBhX8SGo,1023744
torchvision/.dylibs/libjpeg.8.2.2.dylib,sha256=amQT3U7PGpmQlZOlLw6IMmvNUxjI9gLTKGZhK6wLrtI,370368
torchvision/.dylibs/libpng16.16.dylib,sha256=V5dSMJxQUDEv38C7CqTcIwz2BGt9cLVpRcm7SdO5KC8,275376
torchvision/.dylibs/libz.1.2.13.dylib,sha256=oCvZw75Yw_sfTVcMLGOfhCym9-F8h-mbFMCFL9qmcDk,157536
torchvision/_C.so,sha256=KZOffiPjblzi49EiRXEwD5Y7kr1OYoNYA9UJZj8Xf_k,1048384
torchvision/__init__.py,sha256=3AOjL7NqkSGNquYbrRUSIVXwC-8kBp-BZmhMspcnQ_c,3368
torchvision/__pycache__/__init__.cpython-38.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-38.pyc,,
torchvision/__pycache__/_meta_registrations.cpython-38.pyc,,
torchvision/__pycache__/_utils.cpython-38.pyc,,
torchvision/__pycache__/extension.cpython-38.pyc,,
torchvision/__pycache__/utils.cpython-38.pyc,,
torchvision/__pycache__/version.cpython-38.pyc,,
torchvision/_internally_replaced_utils.py,sha256=67zSUHOn6JwdnMUQchHgpNLCtWQQ9dJFpV_OUn8Qb_w,1389
torchvision/_meta_registrations.py,sha256=m85kIoGNmEvs8aGrFswT7404--0xCHnqA4TRmlhbv2s,1613
torchvision/_utils.py,sha256=6TWK0JGaZVQrofgCAp5ox61_NQE2gIwhYouKQMiTaJ8,934
torchvision/datasets/__init__.py,sha256=LMeiFCzzqDloXdMjlNx40O1AkzWXp4KZJ7NUncDYYLQ,3534
torchvision/datasets/__pycache__/__init__.cpython-38.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-38.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-38.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-38.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-38.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-38.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-38.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-38.pyc,,
torchvision/datasets/__pycache__/coco.cpython-38.pyc,,
torchvision/datasets/__pycache__/country211.cpython-38.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-38.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-38.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-38.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-38.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-38.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-38.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-38.pyc,,
torchvision/datasets/__pycache__/folder.cpython-38.pyc,,
torchvision/datasets/__pycache__/food101.cpython-38.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-38.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-38.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-38.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-38.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-38.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-38.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-38.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-38.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-38.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-38.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-38.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-38.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-38.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-38.pyc,,
torchvision/datasets/__pycache__/places365.cpython-38.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-38.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-38.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-38.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-38.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-38.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-38.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-38.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-38.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-38.pyc,,
torchvision/datasets/__pycache__/usps.cpython-38.pyc,,
torchvision/datasets/__pycache__/utils.cpython-38.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-38.pyc,,
torchvision/datasets/__pycache__/vision.cpython-38.pyc,,
torchvision/datasets/__pycache__/voc.cpython-38.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-38.pyc,,
torchvision/datasets/_optical_flow.py,sha256=UeB-0XfjtpTH6bCLYXm21RxscJ3Dyw8urQbIqb0vg_4,19489
torchvision/datasets/_stereo_matching.py,sha256=48CZpvJTW3hf2rPKbjs2HJKnOWuWwAYlmcqWcUpltgc,48774
torchvision/datasets/caltech.py,sha256=YiQMS2pnG4kFT3KyU7YufWJZrgd-0g65LvukRW7Sf60,8738
torchvision/datasets/celeba.py,sha256=ndPKsAdyKj1GAe8Y9Mz-7fIWi7WZwKsewnm9x42dqcU,8292
torchvision/datasets/cifar.py,sha256=TqdSHJ9UBtk0nVvnvlILIKgoGP-GjgbYCqjWhjCvf6I,5789
torchvision/datasets/cityscapes.py,sha256=zRAZzk80EmgQlTK2swfOnYwV-fIXGuQy8JHq1LL-vyo,10238
torchvision/datasets/clevr.py,sha256=jLqbFP5rfn_1mjP5Dc7eSnQKHVHf-TLCpR9aGmbrfuE,3416
torchvision/datasets/coco.py,sha256=8IrwZcvTvfHHatZwEUd8u1dZJHlXRLFqOegKXFO-nJw,3972
torchvision/datasets/country211.py,sha256=NbS59MbCQdy7jbwR4af3sENKEnOGbXoUvMuqxvr1Mzo,2401
torchvision/datasets/dtd.py,sha256=EfOGSCKAD2Cw6MmqiB9tdiVDzov6fqlke5KDa18_oWA,3975
torchvision/datasets/eurosat.py,sha256=cf0RE6i5ngu9cuKFj14-okg7bmLt4VC-5v1fRVd0qtg,2053
torchvision/datasets/fakedata.py,sha256=VIxYzcoNwtgTGBsR9d3dmpYJhJA63iTPvMROqHsXBmk,2481
torchvision/datasets/fer2013.py,sha256=7cYCpTOji2T__c-JaczOu4_BfvH_wZmTYke-2JHWq_o,2762
torchvision/datasets/fgvc_aircraft.py,sha256=FEbGNLlTJKGx5jQeh-o-IamApP7ydIqR-nmh-1NCXp0,4566
torchvision/datasets/flickr.py,sha256=7u7IKEc-KawRlKV2QKdrbG-XCETR7qwF5uTfp7vNOkQ,5339
torchvision/datasets/flowers102.py,sha256=-EpegZlOcVhjt6mNbGMhl25ydxNU5Xx3zt6poUwh9Zw,4605
torchvision/datasets/folder.py,sha256=kyJs1XgN3NxbyG15asCc7NBVH-vSXZ02JaraPfumd4g,11910
torchvision/datasets/food101.py,sha256=b7kXyXrUSJkKVPXyA9q-iRH69-XntXPaSlut0YK0eC8,3717
torchvision/datasets/gtsrb.py,sha256=N17Rq6IQX4oPEAtg-VNqxv789IhSrFKGWmfWAuej1UQ,3742
torchvision/datasets/hmdb51.py,sha256=m9sxwdA67NNG2dt86iWwzJZG2QWulNun_eWmcM1tUUE,5909
torchvision/datasets/imagenet.py,sha256=Qbcq6iwyjx-tsEx_gidWBPMDX9e_2cIBSzOknuqpENo,8487
torchvision/datasets/inaturalist.py,sha256=QR9SDFtBg7a-2w9Muo_8ywJ87Q_Ym8uYaPYnro8Hnys,10107
torchvision/datasets/kinetics.py,sha256=ARFmdqGhFIyGGFlEjK6mbUrvkBzdr07ln52Ll2AYleQ,10333
torchvision/datasets/kitti.py,sha256=pE3w4DIpCLT4OBS97jEQNpNJjWuSUaJoi9PX_pW4Fv0,5600
torchvision/datasets/lfw.py,sha256=6h2y3a094PjFdn_DJX6WGyipiDk_ALk3-ed1wVTl4Yg,10491
torchvision/datasets/lsun.py,sha256=5WOpSAtfzZ3FcuraDHF1SEmhs8M3JUMqrLBCvWJJsWk,5675
torchvision/datasets/mnist.py,sha256=z2sAfRw5NTbH2hDW_KsroilCwb_J38orWv3KLPsmG8Q,21205
torchvision/datasets/moving_mnist.py,sha256=PI5X5JAdYZKpv9yF-Dculrd8C_Q8CbYELWj9JyqqL3Q,3585
torchvision/datasets/omniglot.py,sha256=2nQW1nrmsyXeaXSPfPyTLWIhiMm6rZWKpDfdT8n817M,4091
torchvision/datasets/oxford_iiit_pet.py,sha256=4DCp7HHkx7Jw6uF9iiAQ0zXqfSBO8JnbCPoECfpwok4,5053
torchvision/datasets/pcam.py,sha256=0TJysveXU2wm8yvGDc-hdUZJGby9caGTYvh197lDXfo,5115
torchvision/datasets/phototour.py,sha256=0bLBhwI5lD5fmXyEtKFW0WTC3XQ2lRm433-8c0Y_ZUA,7924
torchvision/datasets/places365.py,sha256=oDbW8TCnEC9yBGg8AR-wuU6YY2iDxWcGiwImLyUXC8Q,7199
torchvision/datasets/rendered_sst2.py,sha256=N5dLI8VgnAsmqgXLoRPDGKGcXggP-3EcEtNP_qenw0I,3562
torchvision/datasets/samplers/__init__.py,sha256=W1ZtQpGLG6aoHylo1t8PEsHIVoWwso5bSFk9JzKfH8g,161
torchvision/datasets/samplers/__pycache__/__init__.cpython-38.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-38.pyc,,
torchvision/datasets/samplers/clip_sampler.py,sha256=1-k3bxToGpBlqC4-iyVDggtojA701NflW0nBRLK27tQ,6244
torchvision/datasets/sbd.py,sha256=4-iQW-j7nhK0FEbwFg2sonyc9TzvUQ20dRezJT2_lyY,5202
torchvision/datasets/sbu.py,sha256=L2axgbql5zryhzmYhaLKXfBlcttIewukYTJQL1C_LUc,4081
torchvision/datasets/semeion.py,sha256=scDtolOsTEqe-wyNocqsTVBX4J6Q4G_7PvF8PkIgI3A,3088
torchvision/datasets/stanford_cars.py,sha256=G2l_QoWfTi_ZiUQDQKZ45KnYBi5m43ie8bExTGUTdvI,4843
torchvision/datasets/stl10.py,sha256=sbrOzrkiCjPslLBBnakZ-63Qxz5GcAIXntnr_XtS7Xc,7233
torchvision/datasets/sun397.py,sha256=BGmuMDZak4qVnRFyHfAAuX-4MqlsdHx6zAd5peD0xxE,2748
torchvision/datasets/svhn.py,sha256=kIuOppQKsmvc3kVPU8KcEGMWH0TkTHS5mHQeg_8hgvo,4768
torchvision/datasets/ucf101.py,sha256=M6r2zlajjMDC5uiFeHeZUooQsvpmNG0ah2EjyZ0TjmQ,5472
torchvision/datasets/usps.py,sha256=slohXYwBzIQtBd3idomN6hSn8U1x3dlB6nmBo_Vw2Ys,3440
torchvision/datasets/utils.py,sha256=qcNQti5JtJFY1AZgZdVmYFVZ_hrlj2PxX_mtH4z-X4M,18340
torchvision/datasets/video_utils.py,sha256=RFdycMlo2trpC0bAvNjsN1DGH3sGNmt_EDn9MFYsEms,17064
torchvision/datasets/vision.py,sha256=huBGqprELs1igoV2A5gZ5ukQhK9XZrxxFD2NbmwfoWw,4135
torchvision/datasets/voc.py,sha256=t8pBKMh-BuOTELjj8m2biyXhJwVGW6z16BvyjrPg-kM,8760
torchvision/datasets/widerface.py,sha256=dFcxBKrAyTx5J_mv42amHeuy2dHnFXQmIhE5RWC3EwI,8117
torchvision/extension.py,sha256=YWBDURfCFXSmRvXi2iEg2L0hafN2-RnybpImh9JAUtQ,3141
torchvision/image.so,sha256=La96A00L7puEAcizYxloh2dIjSbq8jqMDHEtCRKX0P0,192400
torchvision/io/__init__.py,sha256=jRZ5dfLN6oby_jI2cAGIWl_o4GTO_djxVDIpXFElsVU,1478
torchvision/io/__pycache__/__init__.cpython-38.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-38.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-38.pyc,,
torchvision/io/__pycache__/image.cpython-38.pyc,,
torchvision/io/__pycache__/video.cpython-38.pyc,,
torchvision/io/__pycache__/video_reader.cpython-38.pyc,,
torchvision/io/_load_gpu_decoder.py,sha256=nvR0HG0B2-GEYpFiooPELIOgfL6X3gUetPgFGuH4nWs,174
torchvision/io/_video_opt.py,sha256=iClXtPsNK4Fph0-Nzx4MzIBbT-rElpZ6JOKBd8X1Vbk,20390
torchvision/io/image.py,sha256=YOCb9u_yBSA_GP_3sJ8MSpRcGsSd8yKXdlIXnUUX2nA,9878
torchvision/io/video.py,sha256=89WNezkYxKm59GtX__Ta_aaPukD6p7nm7bkuMiV27WI,15674
torchvision/io/video_reader.py,sha256=BfYpiSG8SxmdsAf-Med_7V9MVWNZVxqpIb9i5bqNXuw,11747
torchvision/models/__init__.py,sha256=A8GQPE1bl3oUHpuD9ND53DV557IPY4459FNLW6sVXGI,865
torchvision/models/__pycache__/__init__.cpython-38.pyc,,
torchvision/models/__pycache__/_api.cpython-38.pyc,,
torchvision/models/__pycache__/_meta.cpython-38.pyc,,
torchvision/models/__pycache__/_utils.cpython-38.pyc,,
torchvision/models/__pycache__/alexnet.cpython-38.pyc,,
torchvision/models/__pycache__/convnext.cpython-38.pyc,,
torchvision/models/__pycache__/densenet.cpython-38.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-38.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-38.pyc,,
torchvision/models/__pycache__/googlenet.cpython-38.pyc,,
torchvision/models/__pycache__/inception.cpython-38.pyc,,
torchvision/models/__pycache__/maxvit.cpython-38.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-38.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-38.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-38.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-38.pyc,,
torchvision/models/__pycache__/regnet.cpython-38.pyc,,
torchvision/models/__pycache__/resnet.cpython-38.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-38.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-38.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-38.pyc,,
torchvision/models/__pycache__/vgg.cpython-38.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-38.pyc,,
torchvision/models/_api.py,sha256=uIIJnxX1zYMNpdvJ0haSq15_XlR1QteFZBYVAdtEheg,10054
torchvision/models/_meta.py,sha256=fqpeQBsf9EEYbmApQ8Q0LKyM9_UFwjireII5mwDbwJY,28875
torchvision/models/_utils.py,sha256=S8uDD7maNefy-fEW6mpz8dFU68acK1HxN0kt1qpkkDo,10893
torchvision/models/alexnet.py,sha256=dvBZLVH60TOTHCNNkWg0TFLtuJ5Ghh_xXN73r3Vyq58,4488
torchvision/models/convnext.py,sha256=tP73tH-us6h2KSdVcPypEX9Izk5lsr82KsGT15mj4NE,15326
torchvision/models/densenet.py,sha256=qUhW4pNpZtj8JVkvc2Rjo9svDxL_HMqCqXFWggu9M1o,16804
torchvision/models/detection/__init__.py,sha256=JwYm_fTGO_FeRg4eTOQLwQPZ9lC9jheZ-QEoJgqKTjg,168
torchvision/models/detection/__pycache__/__init__.cpython-38.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-38.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-38.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-38.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-38.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-38.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-38.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-38.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-38.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-38.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-38.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-38.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-38.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-38.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-38.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-38.pyc,,
torchvision/models/detection/_utils.py,sha256=2y3FQ4F5yXhFM7VIWmu_70FpKgZjxdT_ucfzYwi3ZUQ,22127
torchvision/models/detection/anchor_utils.py,sha256=8Ix1Vp3i2kgJGr6esie3rw0_yAjtrUSvLXVKPaoZeQo,11859
torchvision/models/detection/backbone_utils.py,sha256=DsXGcZaZ3G67OkRG-8zmIqL__VSouzhy_LqXesukjl8,10449
torchvision/models/detection/faster_rcnn.py,sha256=OldpozE7KcICV1C70uUHWyTAhr9soAzbT99LSEMtl1g,36767
torchvision/models/detection/fcos.py,sha256=12mQ37D3hpXQ7uGBGyWMtzwgjJQPu6adwvjzB-wxnw0,33990
torchvision/models/detection/generalized_rcnn.py,sha256=4-Dp8Vx-SjDDSZ7TsZ11rmkvEH336aLuSOlERXiQ7fs,4743
torchvision/models/detection/image_list.py,sha256=SUJ3xMn-1xc6ivYZUNIdWBh3RH9xD8EtCdpsXnPI_iM,783
torchvision/models/detection/keypoint_rcnn.py,sha256=8GDpiBk_Rcg0yfCril5GkpGkA4JN4eWihPjIMsup3GM,21760
torchvision/models/detection/mask_rcnn.py,sha256=K3oizvu-Na_Et_e6nZx2flbdKFQaeX6WV5XH-zmVlEU,26501
torchvision/models/detection/retinanet.py,sha256=_31tsj9tjTtrtSY5LUbqHqEXs6vr4RTEi5mR02cGZak,37055
torchvision/models/detection/roi_heads.py,sha256=Uh9950xZUEmejwD2pRRhKvqNV0bY_G2Om8yGC2EdDDg,33822
torchvision/models/detection/rpn.py,sha256=_OFaGmf7cXJSkCJX6tX7OROXIYkTNJ52gQlW-Aofdig,15735
torchvision/models/detection/ssd.py,sha256=tbsgVbRD36WrjkZEB4xi1fvOXT62ry0p8G_Sd-j5CrY,28979
torchvision/models/detection/ssdlite.py,sha256=HQ8XD36fSId_OiMecjkLxcEtQsS-1VvryMzYc8emhCk,13215
torchvision/models/detection/transform.py,sha256=uTOFP37GkNg-qGbb0gYgAF8snE6ndfu6pa6g5U6ng9g,12118
torchvision/models/efficientnet.py,sha256=4qyeoXkYGFyUsBDt8TygDYycMMt1zhGwB_l4PmoPv4g,43090
torchvision/models/feature_extraction.py,sha256=UCoGdvS8_tnuIZp9-YE5atrn96Gk6mZSWAYPti_nnCg,25577
torchvision/models/googlenet.py,sha256=ni7VlSJW2_zG0Adxx56fuN5t4yI6vROBAuAu06-V4f0,12806
torchvision/models/inception.py,sha256=ifrLErzOVG-vlwQOMXLX5yMgcpHxCQQ17L7Wacn5QhQ,18851
torchvision/models/maxvit.py,sha256=CK7u0cZnclBHDaLn7cwEonDgD1o6gtcQIYZxw3nh2rs,31953
torchvision/models/mnasnet.py,sha256=h9jY1TupaChZj9khnXya_l4O1exUWhWOOCmhJCCImKc,17574
torchvision/models/mobilenet.py,sha256=lSRVxw2TL3LFBwCadvyvH6n3GzqUTnK2-rhX3MOgSrs,211
torchvision/models/mobilenetv2.py,sha256=v9cRBAp7_C_50JFkjGZ0luvuh45oCYgYn37pcG2UL8o,9710
torchvision/models/mobilenetv3.py,sha256=-Xk32m_Wdn-ap8wCL4Tl7wjiROIwDwhasInYTMwwOrE,16279
torchvision/models/optical_flow/__init__.py,sha256=0zRlMWQJCjFqoUafUXVgO89-z7em7tACo9E8hHSq9RQ,20
torchvision/models/optical_flow/__pycache__/__init__.cpython-38.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-38.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-38.pyc,,
torchvision/models/optical_flow/_utils.py,sha256=v-tQJzYmYukrD1sQAE-5j5jxyvComwF1UdGkz5tVTLw,2077
torchvision/models/optical_flow/raft.py,sha256=FpSLPXisugu5Rzp_D5XCr037snBapMJ0dDPrw9c3CNk,39995
torchvision/models/quantization/__init__.py,sha256=gqFM7zI4UUHKKBDJAumozOn7xPL0JtvyNS8Ejz6QXp0,125
torchvision/models/quantization/__pycache__/__init__.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-38.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-38.pyc,,
torchvision/models/quantization/googlenet.py,sha256=C-8lm9TnjkEuwu6zaPp0r5mb0QMYvTMGOtz2--s1IFo,8080
torchvision/models/quantization/inception.py,sha256=hg8K1QNk7T-Qo3zOB47eupS3Thu_RjVI6mG2HzAEx8M,10815
torchvision/models/quantization/mobilenet.py,sha256=lSRVxw2TL3LFBwCadvyvH6n3GzqUTnK2-rhX3MOgSrs,211
torchvision/models/quantization/mobilenetv2.py,sha256=ggpNLU4_JkyMn8IPTgj1p0xx_Wvspcii2Wd3ISj5tBE,5883
torchvision/models/quantization/mobilenetv3.py,sha256=PVWmSP62Pn8hQkd682l6uYFLQp1nxZltMOE-FhhO9OU,9230
torchvision/models/quantization/resnet.py,sha256=9Hb6KyPv33Jj1A6JciXvGX06q0RkwwP10u8GxFfmorM,17939
torchvision/models/quantization/shufflenetv2.py,sha256=eS2y34ZTG03dNJgtVJ2qSXQWZ22PHIWBYeC8cbvI1yI,16884
torchvision/models/quantization/utils.py,sha256=n8mWsK9_Ek_M2AqGKPLoLlcKaYGH2PrF2l5_W84oBMk,2058
torchvision/models/regnet.py,sha256=-7s5n0qzXZPR9HgzOk9aj1sv9dWZ3AxnP7CmZRdUeZI,63553
torchvision/models/resnet.py,sha256=dJmlBZrXsaH491Q8BLShN5UUD62DfDhTC0j_XZYQv24,38932
torchvision/models/segmentation/__init__.py,sha256=TGk6UdVXAMtwBpYalrvdXZnmSwqzTDOT1lgKrfzhHrQ,66
torchvision/models/segmentation/__pycache__/__init__.cpython-38.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-38.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-38.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-38.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-38.pyc,,
torchvision/models/segmentation/_utils.py,sha256=QfyqCtH_MJnIkKW5m-98GZD2MjtPYLtPTDi79pcIGhs,1197
torchvision/models/segmentation/deeplabv3.py,sha256=oLXN680kgn9vZl7wKp4iLUpxIQK4brWbH0gYMk5EixM,14963
torchvision/models/segmentation/fcn.py,sha256=I1FqaZZVPc3Fbg_7E2L5qpumnupxBYc7KYsW03EG_Cs,8973
torchvision/models/segmentation/lraspp.py,sha256=dt5DJ_qbDZlEM0SIuN87JU43JHfVlb8Oepp76KDv5tw,7643
torchvision/models/shufflenetv2.py,sha256=84FiPfkhJpSw6Q9Jmaug5MW5qmWCO3VhAPF61EiMn7Q,15444
torchvision/models/squeezenet.py,sha256=apjFPEI5nr_493bAQsR245EorzaMYXVQSqdcveyAfy0,8763
torchvision/models/swin_transformer.py,sha256=VwvnImWcjblashj0OONycDJnIkz-zRDpm365v_a0-zo,39337
torchvision/models/vgg.py,sha256=jYjIoY2jtKAc-aURCQsvbgBxup1Gh4fVZSt2NzFLlZY,19225
torchvision/models/video/__init__.py,sha256=O4HB-RaXgCtnvpMDAuMBaIeKIiYEkNxra_fmAHLUIJM,93
torchvision/models/video/__pycache__/__init__.cpython-38.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-38.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-38.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-38.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-38.pyc,,
torchvision/models/video/mvit.py,sha256=0AZ31K5QcUBWZUUPTI1FCCM2Fma95bPs1o82zzpw2i0,32998
torchvision/models/video/resnet.py,sha256=RUnbUXFmoWNo_XbEKLmVSM8LUDcyv6jGZJ8GGpZi_6U,16771
torchvision/models/video/s3d.py,sha256=jx9gMP18Bzb7UO3vjejVBHlrCrJPdWFDfTn7XeU5kMg,7815
torchvision/models/video/swin_transformer.py,sha256=3GMyPGPeMcwJ1p9TGiRbpIlP-G7Qv_jWNbZmqIwMNyA,27688
torchvision/models/vision_transformer.py,sha256=O4mdBjYFsp-HTZA9bXfux_wJzIPRv1uS43PjuNh52zc,32136
torchvision/ops/__init__.py,sha256=eVv16QSBwgKaojOUHMPCy4ou9ZeFh-HoCV4DpqrZG4U,1928
torchvision/ops/__pycache__/__init__.cpython-38.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-38.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-38.pyc,,
torchvision/ops/__pycache__/_utils.cpython-38.pyc,,
torchvision/ops/__pycache__/boxes.cpython-38.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-38.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-38.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-38.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-38.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-38.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-38.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-38.pyc,,
torchvision/ops/__pycache__/misc.cpython-38.pyc,,
torchvision/ops/__pycache__/poolers.cpython-38.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-38.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-38.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-38.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-38.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-38.pyc,,
torchvision/ops/_box_convert.py,sha256=_bRRpErwk03rcPuscO1tCI9v3l88oNlDBDl2jzPlbKo,2409
torchvision/ops/_register_onnx_ops.py,sha256=Fyb1kC2m2OqZdfW_M86pt9-S66e1qNUhXNu1EQRa034,4181
torchvision/ops/_utils.py,sha256=pVHPpsmx6XcfGjUVk-XAEnd8QJBkrw_cT6fO_IwICE4,3630
torchvision/ops/boxes.py,sha256=_IpyT5nPIt0E9byfI4rjs8aXl-UQNGngLZZYiDYwqEg,15495
torchvision/ops/ciou_loss.py,sha256=3HClrMMKOJ3bndIUinNp3cp6Cim4-ZmmfuLn1-NPDUo,2756
torchvision/ops/deform_conv.py,sha256=fJxkVR_p_OQMzMja4flvmTgqDPvrOOcwzDG8bV7Q7pE,6990
torchvision/ops/diou_loss.py,sha256=tssNJhII4WT-wmidFS8gFNteQIAJz-Nd1Q7Trz1BjIY,3362
torchvision/ops/drop_block.py,sha256=A4EGIl7txrU_QmkI1N0W9hfd8tq8yx6zq32oYXaddLQ,5855
torchvision/ops/feature_pyramid_network.py,sha256=mfkaygxRz-0TAdTMq2fCAL-E0WxlRnTfdb-s_J5qPE4,8702
torchvision/ops/focal_loss.py,sha256=9kFqGyA0-hodRw9Au74k-FuS14OhsAvbFxDGvpx08Sg,2261
torchvision/ops/giou_loss.py,sha256=OXSaMZDZ0qy7jgaQ9exB_DMQXzcATBAFiIjzSlOV-bQ,2696
torchvision/ops/misc.py,sha256=yFnK7GT9OCMfDrn4NtQXKdh5broi1xocL94SoyqhWuw,13572
torchvision/ops/poolers.py,sha256=zzYhH7poMwGlYxDvAvCaL9emg9X7sM4xZFLEy0zvv5s,11920
torchvision/ops/ps_roi_align.py,sha256=4iAbeUVTessAcxvJhuARN_aFGUTZC9R4KrKC_mBH3MQ,3625
torchvision/ops/ps_roi_pool.py,sha256=jOv-2pAZdLFvvt4r4NwiRfxU5WAOy_vi6gxZjMvlusw,2870
torchvision/ops/roi_align.py,sha256=wKwVi4cMUKQjvmJlnSYHOTrZX081YfCzxOjKOuXqL1M,10756
torchvision/ops/roi_pool.py,sha256=70ou6Xc7qJxKe3SC54QIW3L99PoS0gLlwGocaYDbD2w,2943
torchvision/ops/stochastic_depth.py,sha256=ISZ9noJyZLxpTG-wa2VmPs66qjhVsP7ZxWHvumWSP3U,2236
torchvision/transforms/__init__.py,sha256=EMft42B1JAiU11J1rxIN4Znis6EJPbp-bsGjAzH-24M,53
torchvision/transforms/__pycache__/__init__.cpython-38.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-38.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-38.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-38.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-38.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-38.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-38.pyc,,
torchvision/transforms/__pycache__/functional.cpython-38.pyc,,
torchvision/transforms/__pycache__/functional_pil.cpython-38.pyc,,
torchvision/transforms/__pycache__/functional_tensor.cpython-38.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-38.pyc,,
torchvision/transforms/_functional_pil.py,sha256=nmvbsk0KIjKDZ1iSPwiuFHNWbGvMmTeYdeoHhYXPolM,12112
torchvision/transforms/_functional_tensor.py,sha256=kduGiDVUmebduNn8D725iy8eQG0_drFkEA2279Op85Q,33965
torchvision/transforms/_functional_video.py,sha256=YcV557YglbJsq9SRGJHFoRbtxawiLSJ1oM5rV75OyqQ,3857
torchvision/transforms/_presets.py,sha256=gak5ey_hchoWHlIkgMu42Hktava4CP4Ic7fAn8c4kOQ,8519
torchvision/transforms/_transforms_video.py,sha256=Buz5LCWVPGiEonHE-cXIXfbkBhNc0qxVraxkNdxKp8o,4950
torchvision/transforms/autoaugment.py,sha256=JcbdEDbR0-OqTE4cwkhVSB45woFZQ_Fq5xmjFu_3bjg,28243
torchvision/transforms/functional.py,sha256=-0cOMDvENPK9rm8SkPABObEnfnLrOeXP9nIPSMEMRmU,69460
torchvision/transforms/functional_pil.py,sha256=L9MWmbNyaSrLGtPkjKWcjBfxv2FHUvq-knaivWw-P68,375
torchvision/transforms/functional_tensor.py,sha256=Ff9TQBjf5h3OIn2wJbqUBrZYF0oH6gzoy2zCUCptGgs,381
torchvision/transforms/transforms.py,sha256=XkgckEtkoV4GLB9dafS8y14k8B5jkzqHBYaWlsmjewU,86099
torchvision/transforms/v2/__init__.py,sha256=9HBFpAWEINr9uA25guqjOKIVz5fHcqriu7BCp1hkHV0,1419
torchvision/transforms/v2/__pycache__/__init__.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-38.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-38.pyc,,
torchvision/transforms/v2/_augment.py,sha256=idGM3f-5eVCQpVLVcGakE0ZYo0TMHSYIfTw6Hb5sCO8,13943
torchvision/transforms/v2/_auto_augment.py,sha256=ocHnu_yy_YBGekQOIekD9xhkljLjjVF6SDoFAGtMaF8,31990
torchvision/transforms/v2/_color.py,sha256=5usdEaSfeApXCFiwcodTQaeVINN4SMVOKesErtWgGlY,16976
torchvision/transforms/v2/_container.py,sha256=hNpsb1dW69DkhaKxpCVGtR1VRLKHeTL1fE1TbvSFTkk,6124
torchvision/transforms/v2/_deprecated.py,sha256=JYLl5bix66INZyuCs8vWm6BLR75v5TCYm_5jowFxlkQ,1886
torchvision/transforms/v2/_geometry.py,sha256=TjiElybunPYEC4XzkuFlo1SnlQd9QemgSRB36XFMdsE,68873
torchvision/transforms/v2/_meta.py,sha256=cIbr_wuOQRjc9gUjaOKHgqo4gD5-uhdDDa-qCYp_SOc,1613
torchvision/transforms/v2/_misc.py,sha256=oHvQL0MRJ6vKFV2pIVEzlnhOnv5vplq6cyVjZaffwd4,17505
torchvision/transforms/v2/_temporal.py,sha256=d7xGjo4J2hAXNdlHpYXz4DbnaJQR9I_9K0rl9kJeof0,971
torchvision/transforms/v2/_transform.py,sha256=008PBMswQWIc7dEmhWqm772_O4ciDY3rycGu08nhcME,8476
torchvision/transforms/v2/_type_conversion.py,sha256=h8AZOmavlY7S9hM89SiXDsTTKTFrYx0ZDTJ7QVLOL8s,3065
torchvision/transforms/v2/_utils.py,sha256=KSkGow8EwtP4OMwdtd6En1b08EA-PTKVZH36FV7IUSQ,8706
torchvision/transforms/v2/functional/__init__.py,sha256=QROAo8DCNo5i3Kp1XKuf1U0k6ThVRq93Z5Dwf40ptUI,4217
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-38.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-38.pyc,,
torchvision/transforms/v2/functional/_augment.py,sha256=3tMaAdavMpggF7bUMecFL0DQJDTikQyLaJxCl-uSsgs,1688
torchvision/transforms/v2/functional/_color.py,sha256=ELB16eTBijpesAwvytsGJbheeoJVtXrSDH5xUr_A0HI,29038
torchvision/transforms/v2/functional/_deprecated.py,sha256=8pFCBFDzZtgfRCi3hPtu_-tE8alkKkA_a4W5kqDPqGM,808
torchvision/transforms/v2/functional/_geometry.py,sha256=iauGMa_ZwLZOa3W-fKiUQ5kdEy8KssS-VHPSfORlD4c,86888
torchvision/transforms/v2/functional/_meta.py,sha256=Nmovkcb4owIO-QuHCId4UL_WWTy1qZE84mOcSxugrFM,10369
torchvision/transforms/v2/functional/_misc.py,sha256=zvB_nwp-z6J3t9ATIuYrIQYPcTOo1fChdnP3MShZqrg,10734
torchvision/transforms/v2/functional/_temporal.py,sha256=wPkcULPTklKQz7GebwYn5GAPoCIkmHp-zjfw8AVJcOU,1143
torchvision/transforms/v2/functional/_type_conversion.py,sha256=Ac7NU4ffLRQse3UbKmr6w0HYKg-XOHSY5ye0l0PAJS8,823
torchvision/transforms/v2/functional/_utils.py,sha256=oQX6L0E3QvqmeHacJqFJsYEJghFKWZ_0o96JLK7prAw,5486
torchvision/tv_tensors/__init__.py,sha256=-cZsQW7sPqK8bwLqU2sDM8_viMWg8TpXOs5rnAJPY7c,1260
torchvision/tv_tensors/__pycache__/__init__.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_image.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_mask.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-38.pyc,,
torchvision/tv_tensors/__pycache__/_video.cpython-38.pyc,,
torchvision/tv_tensors/_bounding_boxes.py,sha256=FT4Dde-XgXuDVRShQ_9AclKzPpW6Dw35LOyQlk7FalI,4485
torchvision/tv_tensors/_dataset_wrapper.py,sha256=gxq64QxUPwLbQOMg222W2s2GqUgVcLDlo8oKL2eyr08,24260
torchvision/tv_tensors/_image.py,sha256=SgPdTyVRVXZxCH66MTuiZzIkqifymWsbPQkNDyeN0mo,1911
torchvision/tv_tensors/_mask.py,sha256=sX2AzNgtun_97GJbPAeOO75KRE88geaIO7jtEoZXRt4,1458
torchvision/tv_tensors/_torch_function_helpers.py,sha256=94xC6gOesZDGdsvoThqdKs3ZLa5vZyWojs2QY_vjEhg,2283
torchvision/tv_tensors/_tv_tensor.py,sha256=xcEMTg5ijjFvhcbtIKgwfGsVZ6Vvl5PKo-OdEvMuquc,6255
torchvision/tv_tensors/_video.py,sha256=K41DsTs4-9oJQnHz1S5zHsGWZsED-lSPxWxdk81akPo,1390
torchvision/utils.py,sha256=fwpoqLk5EIvN8h91kkzg2IiOD_8F3w11L0YZTTX8XAo,23512
torchvision/version.py,sha256=YITkonfAhNvmzR4o5FeVJHozP09WyaRi2Nan-ui65ps,197
