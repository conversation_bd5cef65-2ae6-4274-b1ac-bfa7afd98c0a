#!/usr/bin/env python3
"""
Baseline Test for Grid2Op Environment

This script tests the basic environment stability with do-nothing actions
to establish a baseline before training complex agents.

Author: Grid Agent Project
Date: 2025-07-13
"""

import grid2op
from lightsim2grid import LightSimBackend
from custom_rewards import StabilityReward
import numpy as np


def test_do_nothing_baseline(num_episodes=5, max_steps_per_episode=100):
    """
    Test the environment with only do-nothing actions to establish baseline performance.
    
    Parameters
    ----------
    num_episodes : int
        Number of episodes to test
    max_steps_per_episode : int
        Maximum steps per episode
    """
    
    print("🧪 Testing Do-Nothing Baseline")
    print("=" * 40)
    
    # Create environment
    env = grid2op.make(
        "l2rpn_case14_sandbox",
        backend=LightSimBackend(),
        reward_class=StabilityReward
    )
    
    episode_rewards = []
    episode_lengths = []
    
    for episode in range(num_episodes):
        print(f"\n📊 Episode {episode + 1}/{num_episodes}")
        
        obs = env.reset()
        episode_reward = 0
        step_count = 0
        
        print(f"   Initial max line loading: {obs.rho.max():.3f}")
        
        for step in range(max_steps_per_episode):
            # Always take do-nothing action
            action = env.action_space()
            
            obs, reward, done, info = env.step(action)
            episode_reward += reward
            step_count += 1
            
            # Print progress every 20 steps
            if step % 20 == 0:
                print(f"   Step {step}: reward={reward:.2f}, max_rho={obs.rho.max():.3f}, done={done}")
            
            if done:
                print(f"   Episode ended at step {step_count}")
                break
        
        episode_rewards.append(episode_reward)
        episode_lengths.append(step_count)
        
        print(f"   Final reward: {episode_reward:.2f}")
        print(f"   Episode length: {step_count}")
        
        if done and step_count < 10:
            print(f"   ⚠️  Very short episode - possible environment instability")
    
    # Summary statistics
    print(f"\n📈 Baseline Results Summary:")
    print(f"   • Average reward: {np.mean(episode_rewards):.2f} ± {np.std(episode_rewards):.2f}")
    print(f"   • Average length: {np.mean(episode_lengths):.1f} ± {np.std(episode_lengths):.1f}")
    print(f"   • Max reward: {np.max(episode_rewards):.2f}")
    print(f"   • Min reward: {np.min(episode_rewards):.2f}")
    print(f"   • Max length: {np.max(episode_lengths)}")
    print(f"   • Min length: {np.min(episode_lengths)}")
    
    # Assessment
    avg_reward = np.mean(episode_rewards)
    avg_length = np.mean(episode_lengths)
    
    print(f"\n🎯 Assessment:")
    if avg_reward > 0 and avg_length > 50:
        print(f"   ✅ Excellent baseline! Environment is stable with do-nothing actions.")
        print(f"   🚀 Ready for agent training.")
    elif avg_reward > -1 and avg_length > 20:
        print(f"   👍 Good baseline. Environment is reasonably stable.")
        print(f"   🔧 Agent training should work with current reward settings.")
    elif avg_reward > -5 and avg_length > 10:
        print(f"   ⚠️  Moderate baseline. Some instability detected.")
        print(f"   🔧 Consider adjusting reward function or environment settings.")
    else:
        print(f"   ❌ Poor baseline. Environment appears unstable even with do-nothing.")
        print(f"   🔧 Need to investigate environment setup or reward function.")
    
    env.close()
    return episode_rewards, episode_lengths


def test_random_actions(num_episodes=3, max_steps_per_episode=50):
    """
    Test with random actions to see how the environment responds.
    """
    
    print(f"\n🎲 Testing Random Actions (for comparison)")
    print("=" * 45)
    
    env = grid2op.make(
        "l2rpn_case14_sandbox",
        backend=LightSimBackend(),
        reward_class=StabilityReward
    )
    
    episode_rewards = []
    episode_lengths = []
    
    for episode in range(num_episodes):
        print(f"\n📊 Random Episode {episode + 1}/{num_episodes}")
        
        obs = env.reset()
        episode_reward = 0
        step_count = 0
        
        for step in range(max_steps_per_episode):
            # Take random action
            action = env.action_space.sample()
            
            obs, reward, done, info = env.step(action)
            episode_reward += reward
            step_count += 1
            
            if step % 10 == 0:
                print(f"   Step {step}: reward={reward:.2f}, done={done}")
            
            if done:
                break
        
        episode_rewards.append(episode_reward)
        episode_lengths.append(step_count)
        
        print(f"   Random episode reward: {episode_reward:.2f}, length: {step_count}")
    
    print(f"\n📈 Random Actions Summary:")
    print(f"   • Average reward: {np.mean(episode_rewards):.2f}")
    print(f"   • Average length: {np.mean(episode_lengths):.1f}")
    
    env.close()
    return episode_rewards, episode_lengths


def main():
    """Run baseline tests."""
    
    print("🔬 Grid2Op Environment Baseline Testing")
    print("=" * 50)
    
    # Test 1: Do-nothing baseline
    do_nothing_rewards, do_nothing_lengths = test_do_nothing_baseline(
        num_episodes=5, 
        max_steps_per_episode=100
    )
    
    # Test 2: Random actions (for comparison)
    random_rewards, random_lengths = test_random_actions(
        num_episodes=3,
        max_steps_per_episode=50
    )
    
    # Comparison
    print(f"\n🔍 Comparison:")
    print(f"   • Do-nothing avg reward: {np.mean(do_nothing_rewards):.2f}")
    print(f"   • Random actions avg reward: {np.mean(random_rewards):.2f}")
    print(f"   • Do-nothing avg length: {np.mean(do_nothing_lengths):.1f}")
    print(f"   • Random actions avg length: {np.mean(random_lengths):.1f}")
    
    if np.mean(do_nothing_rewards) > np.mean(random_rewards):
        print(f"   ✅ Do-nothing is better than random (good sign!)")
    else:
        print(f"   ⚠️  Random actions perform better than do-nothing")
    
    print(f"\n🎉 Baseline testing completed!")
    print(f"💡 Use these results to calibrate your agent training expectations.")


if __name__ == "__main__":
    main()
