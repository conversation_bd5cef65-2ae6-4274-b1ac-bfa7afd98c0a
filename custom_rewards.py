#!/usr/bin/env python3
"""
Custom Reward Functions for Grid2Op Agents

This module contains custom reward functions designed for power grid
topology optimization. The rewards focus on grid stability, safety,
and efficient operation while encouraging conservative agent behavior.

Author: Grid Agent Project
Date: 2025-07-13
"""

import numpy as np
from grid2op.Reward import BaseReward
from typing import Optional


class StabilityReward(BaseReward):
    """
    Stability-focused reward function for power grid topology optimization.
    
    This reward function encourages grid stability by:
    1. Providing survival bonuses for maintaining grid operation
    2. Penalizing high line loadings (risk penalty)
    3. Discouraging unnecessary interventions
    4. Heavily penalizing game over conditions
    
    The reward uses continuous, differentiable functions to provide
    smooth gradients for reinforcement learning algorithms.
    """
    
    def __init__(self, logger: Optional[object] = None):
        """
        Initialize the Stability Reward function.
        
        Parameters
        ----------
        logger : object, optional
            Logger instance for debugging (Grid2Op compatibility)
        """
        super(StabilityReward, self).__init__(logger)
        
        # Reward/penalty weight constants
        self.survival_bonus = 0.1  # Small positive reward for staying alive
        self.risk_penalty_factor = 0.5  # Weight for risk penalty
        self.action_penalty = 0.02  # Small penalty for non-do-nothing actions
        self.game_over_penalty = -100.0  # Large penalty for game over
        
        # Safety thresholds
        self.safety_threshold = 0.9  # Line loading threshold for risk penalty
        self.critical_threshold = 0.95  # Critical line loading threshold
        
        # Additional penalty factors
        self.critical_penalty_factor = 2.0  # Extra penalty for critical loadings
        self.voltage_penalty_factor = 0.1  # Penalty for voltage violations
        
        print(f"🎯 StabilityReward initialized:")
        print(f"   • Survival bonus: +{self.survival_bonus}")
        print(f"   • Risk penalty factor: {self.risk_penalty_factor}")
        print(f"   • Action penalty: -{self.action_penalty}")
        print(f"   • Game over penalty: {self.game_over_penalty}")
        print(f"   • Safety threshold: {self.safety_threshold}")
        print(f"   • Critical threshold: {self.critical_threshold}")
    
    def initialize(self, env):
        """
        Initialize the reward function with environment-specific parameters.
        
        Parameters
        ----------
        env : grid2op.Environment
            The Grid2Op environment instance
        """
        super().initialize(env)
        
        # Set reward bounds based on environment properties
        self.reward_min = self.game_over_penalty  # Minimum possible reward
        self.reward_max = self.survival_bonus + 0.5  # Maximum reasonable reward
        
        # Store environment properties for reference
        self.n_line = env.n_line
        self.n_sub = env.n_sub
        self.n_gen = env.n_gen
        self.n_load = env.n_load
        
        print(f"🔧 StabilityReward initialized for environment:")
        print(f"   • Lines: {self.n_line}")
        print(f"   • Substations: {self.n_sub}")
        print(f"   • Generators: {self.n_gen}")
        print(f"   • Loads: {self.n_load}")
        print(f"   • Reward range: [{self.reward_min:.2f}, {self.reward_max:.2f}]")
    
    def __call__(self, action, env, has_error, is_done, is_illegal, is_ambiguous):
        """
        Compute the reward for the current timestep.
        
        Parameters
        ----------
        action : grid2op.Action.BaseAction
            The action taken by the agent
        env : grid2op.Environment
            The environment instance
        has_error : bool
            Whether an error occurred during the step
        is_done : bool
            Whether the episode has ended
        is_illegal : bool
            Whether the action was illegal
        is_ambiguous : bool
            Whether the action was ambiguous
            
        Returns
        -------
        float
            The computed reward for this timestep
        """
        
        # Get current observation
        observation = env.current_obs
        
        # Step 1: Check for game over conditions
        if has_error or is_done or is_illegal:
            print(f"⚠️  Game over condition detected:")
            print(f"   • Has error: {has_error}")
            print(f"   • Is done: {is_done}")
            print(f"   • Is illegal: {is_illegal}")
            return self.game_over_penalty
        
        # Step 2: Initialize with survival bonus
        reward = self.survival_bonus
        
        # Step 3: Implement risk penalty based on line loadings
        max_rho = observation.rho.max()
        
        # Safety threshold penalty (continuous and smooth)
        if max_rho > self.safety_threshold:
            safety_violation = max_rho - self.safety_threshold
            risk_penalty = -self.risk_penalty_factor * (safety_violation ** 2)
            reward += risk_penalty
            
            # Additional critical penalty for very high loadings
            if max_rho > self.critical_threshold:
                critical_violation = max_rho - self.critical_threshold
                critical_penalty = -self.critical_penalty_factor * (critical_violation ** 3)
                reward += critical_penalty
        
        # Step 4: Voltage stability penalty
        voltage_penalty = self._compute_voltage_penalty(observation)
        reward += voltage_penalty
        
        # Step 5: Action penalty for non-do-nothing actions
        if not self._is_do_nothing_action(action):
            reward -= self.action_penalty
        
        # Step 6: Additional stability bonuses
        stability_bonus = self._compute_stability_bonus(observation)
        reward += stability_bonus
        
        # Ensure reward is within bounds
        reward = np.clip(reward, self.reward_min, self.reward_max)
        
        return float(reward)
    
    def _compute_voltage_penalty(self, observation) -> float:
        """
        Compute penalty for voltage violations.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            Current grid observation
            
        Returns
        -------
        float
            Voltage penalty (negative value)
        """
        penalty = 0.0
        
        # Check generator voltages
        if hasattr(observation, 'gen_v') and observation.gen_v is not None:
            # Penalize voltages outside normal range (0.95 - 1.05 pu)
            gen_v_violations = np.maximum(0, np.abs(observation.gen_v - 1.0) - 0.05)
            penalty -= self.voltage_penalty_factor * np.sum(gen_v_violations ** 2)
        
        # Check load voltages
        if hasattr(observation, 'load_v') and observation.load_v is not None:
            load_v_violations = np.maximum(0, np.abs(observation.load_v - 1.0) - 0.05)
            penalty -= self.voltage_penalty_factor * np.sum(load_v_violations ** 2)
        
        return penalty
    
    def _compute_stability_bonus(self, observation) -> float:
        """
        Compute bonus for maintaining grid stability.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            Current grid observation
            
        Returns
        -------
        float
            Stability bonus (positive value)
        """
        bonus = 0.0
        
        # Bonus for low maximum line loading
        max_rho = observation.rho.max()
        if max_rho < 0.8:  # Well below safety threshold
            safety_margin = 0.8 - max_rho
            bonus += 0.05 * safety_margin  # Small bonus for safety margin
        
        # Bonus for balanced line loadings (low variance)
        rho_std = observation.rho.std()
        if rho_std < 0.1:  # Low variance indicates balanced loading
            bonus += 0.02 * (0.1 - rho_std)
        
        # Bonus for no lines in cooldown
        if hasattr(observation, 'time_before_cooldown_line'):
            lines_in_cooldown = np.sum(observation.time_before_cooldown_line > 0)
            if lines_in_cooldown == 0:
                bonus += 0.01  # Small bonus for no cooldowns
        
        return bonus
    
    def _is_do_nothing_action(self, action) -> bool:
        """
        Check if the action is a do-nothing action.
        
        Parameters
        ----------
        action : grid2op.Action.BaseAction
            The action to check
            
        Returns
        -------
        bool
            True if the action is do-nothing, False otherwise
        """
        try:
            # Check if action has any effect
            if hasattr(action, '_modif_set_bus') and action._modif_set_bus:
                return False
            if hasattr(action, '_modif_change_bus') and action._modif_change_bus:
                return False
            if hasattr(action, '_modif_set_status') and action._modif_set_status:
                return False
            if hasattr(action, '_modif_change_status') and action._modif_change_status:
                return False
            if hasattr(action, '_modif_redispatch') and action._modif_redispatch:
                return False
            
            return True  # No modifications detected
            
        except Exception:
            # Fallback: assume it's not do-nothing if we can't determine
            return False
    
    def get_reward_info(self) -> dict:
        """
        Get information about the reward function configuration.
        
        Returns
        -------
        dict
            Dictionary containing reward function parameters
        """
        return {
            "reward_type": "StabilityReward",
            "survival_bonus": self.survival_bonus,
            "risk_penalty_factor": self.risk_penalty_factor,
            "action_penalty": self.action_penalty,
            "game_over_penalty": self.game_over_penalty,
            "safety_threshold": self.safety_threshold,
            "critical_threshold": self.critical_threshold,
            "reward_range": [self.reward_min, self.reward_max]
        }


class EfficiencyReward(BaseReward):
    """
    Efficiency-focused reward function emphasizing power loss minimization.
    
    This reward function encourages efficient grid operation by:
    1. Minimizing power losses
    2. Maintaining voltage stability
    3. Balancing load distribution
    4. Reducing unnecessary switching
    """
    
    def __init__(self, logger: Optional[object] = None):
        """Initialize the Efficiency Reward function."""
        super(EfficiencyReward, self).__init__(logger)
        
        self.loss_penalty_factor = 1.0
        self.voltage_bonus_factor = 0.1
        self.balance_bonus_factor = 0.05
        self.switching_penalty = 0.1
        
        print(f"⚡ EfficiencyReward initialized with loss minimization focus")
    
    def initialize(self, env):
        """Initialize with environment parameters."""
        super().initialize(env)
        self.reward_min = -50.0
        self.reward_max = 10.0
        
        # Store baseline power losses for comparison
        self.baseline_losses = None
    
    def __call__(self, action, env, has_error, is_done, is_illegal, is_ambiguous):
        """Compute efficiency-based reward."""
        if has_error or is_done or is_illegal:
            return -50.0
        
        observation = env.current_obs
        reward = 0.0
        
        # Power loss penalty
        total_losses = self._compute_power_losses(observation)
        if self.baseline_losses is None:
            self.baseline_losses = total_losses
        
        loss_ratio = total_losses / (self.baseline_losses + 1e-6)
        reward -= self.loss_penalty_factor * max(0, loss_ratio - 1.0)
        
        # Voltage stability bonus
        voltage_bonus = self._compute_voltage_bonus(observation)
        reward += voltage_bonus
        
        # Load balance bonus
        balance_bonus = self._compute_balance_bonus(observation)
        reward += balance_bonus
        
        # Switching penalty
        if not self._is_do_nothing_action(action):
            reward -= self.switching_penalty
        
        return float(np.clip(reward, self.reward_min, self.reward_max))
    
    def _compute_power_losses(self, observation) -> float:
        """Compute total power losses in the grid."""
        # Simplified power loss calculation
        # In practice, this would use more sophisticated power flow analysis
        total_generation = np.sum(observation.gen_p)
        total_load = np.sum(observation.load_p)
        losses = total_generation - total_load
        return max(0, losses)  # Ensure non-negative
    
    def _compute_voltage_bonus(self, observation) -> float:
        """Compute bonus for maintaining good voltage levels."""
        bonus = 0.0
        
        # Bonus for voltages close to nominal (1.0 pu)
        if hasattr(observation, 'gen_v') and observation.gen_v is not None:
            voltage_deviations = np.abs(observation.gen_v - 1.0)
            bonus += self.voltage_bonus_factor * np.exp(-np.mean(voltage_deviations))
        
        return bonus
    
    def _compute_balance_bonus(self, observation) -> float:
        """Compute bonus for balanced load distribution."""
        # Bonus for low variance in line loadings
        rho_variance = np.var(observation.rho)
        balance_bonus = self.balance_bonus_factor * np.exp(-rho_variance * 10)
        return balance_bonus
    
    def _is_do_nothing_action(self, action) -> bool:
        """Check if action is do-nothing (same as StabilityReward)."""
        try:
            if hasattr(action, '_modif_set_bus') and action._modif_set_bus:
                return False
            if hasattr(action, '_modif_change_bus') and action._modif_change_bus:
                return False
            return True
        except Exception:
            return False


def test_stability_reward():
    """
    Test function for the StabilityReward implementation.
    """
    print("🧪 Testing StabilityReward Implementation")
    print("=" * 45)
    
    try:
        import grid2op
        from lightsim2grid import LightSimBackend
        
        # Create environment
        env = grid2op.make("l2rpn_case14_sandbox", backend=LightSimBackend())
        
        # Create reward function
        reward_func = StabilityReward()
        reward_func.initialize(env)
        
        # Test with a few steps
        obs = env.reset()
        
        # Test do-nothing action
        action = env.action_space()
        obs, reward, done, info = env.step(action)
        
        print(f"📊 Test Results:")
        print(f"   • Do-nothing reward: {reward:.4f}")
        print(f"   • Max line loading: {obs.rho.max():.4f}")
        print(f"   • Episode done: {done}")
        
        # Test reward info
        reward_info = reward_func.get_reward_info()
        print(f"   • Reward type: {reward_info['reward_type']}")
        print(f"   • Reward range: {reward_info['reward_range']}")
        
        print(f"✅ StabilityReward test completed successfully!")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ StabilityReward test failed: {e}")
        return False


if __name__ == "__main__":
    # Run test when script is executed directly
    test_stability_reward()
