#!/usr/bin/env python3
"""
Greedy Baseline Agent for Grid2Op

This script implements a simple greedy baseline agent that attempts to manage
power grid topology by switching bus assignments when lines become overloaded.

The agent uses a heuristic approach:
- Monitor line loading (rho values)
- When a line is overloaded (rho > 0.95), switch the bus assignment of its origin
- This attempts to reroute power flow and reduce congestion

Author: Grid Agent Project
Date: 2025-07-13
"""

import grid2op
from lightsim2grid import LightSimBackend
from grid2op.Agent import BaseAgent
from grid2op.Runner import Runner
import numpy as np


class GreedyBaselineAgent(BaseAgent):
    """
    A greedy baseline agent that performs simple topology actions to manage line overloads.
    
    The agent implements a reactive strategy:
    1. Monitor line loading (rho values)
    2. If any line is overloaded (rho > 0.95), find the most overloaded line
    3. Switch the bus assignment of the origin end of that line
    4. If no overload, do nothing
    """
    
    def __init__(self, action_space):
        """
        Initialize the greedy baseline agent.
        
        Parameters
        ----------
        action_space : grid2op.Action.ActionSpace
            The action space of the environment
        """
        super().__init__(action_space)
        self.name = "GreedyBaselineAgent"
        
    def act(self, observation, reward, done=False):
        """
        Choose an action based on the current observation.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            Current state of the power grid
        reward : float
            Reward received from the previous action
        done : bool
            Whether the episode has ended
            
        Returns
        -------
        action : grid2op.Action.BaseAction
            The action to take
        """
        
        # Step 1: Check line loading (rho values)
        rho = observation.rho
        max_rho = np.max(rho)
        
        # Step 2: If maximum rho > 0.95, find the most overloaded line
        if max_rho > 0.95:
            # Find the ID of the most overloaded line
            most_overloaded_line_id = np.argmax(rho)
            
            print(f"⚠️  Line {most_overloaded_line_id} is overloaded with rho = {max_rho:.3f}")
            
            # Step 3: Get current bus assignment for the origin of this line
            # The topology vector tells us which bus each element is connected to
            # We need to find where this line's origin is in the topology vector
            
            # Get the position of this line's origin in the topology vector
            line_or_pos = observation.line_or_pos_topo_vect[most_overloaded_line_id]
            current_bus = observation.topo_vect[line_or_pos]
            
            # Determine the new bus assignment (switch between bus 1 and 2)
            if current_bus == 1:
                new_bus = 2
            elif current_bus == 2:
                new_bus = 1
            else:
                # If disconnected (bus = 0) or unknown, connect to bus 1
                new_bus = 1
            
            print(f"🔄 Switching line {most_overloaded_line_id} origin from bus {current_bus} to bus {new_bus}")
            
            # Step 4: Create topology action to change bus assignment
            try:
                action = self.action_space({
                    "set_bus": {
                        "lines_or_id": [(most_overloaded_line_id, new_bus)]
                    }
                })
                print(f"✅ Created topology action for line {most_overloaded_line_id}")
                
            except Exception as e:
                print(f"❌ Error creating topology action: {e}")
                # Fallback to do-nothing action
                action = self.action_space()
                
        else:
            # Step 5: No line is overloaded, create a "do-nothing" action
            action = self.action_space()
            if max_rho > 0.8:  # Still warn if getting close to overload
                print(f"📊 Maximum line loading: {max_rho:.3f} (no action needed)")
        
        return action
    
    def reset(self, observation):
        """
        Reset the agent at the beginning of a new episode.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            Initial observation of the new episode
        """
        print(f"🔄 {self.name} reset for new episode")
        print(f"📊 Initial max line loading: {np.max(observation.rho):.3f}")


def main():
    """
    Main execution function that sets up and runs the greedy baseline agent.
    """
    
    print("🤖 Greedy Baseline Agent Runner")
    print("=" * 40)
    
    # Step 1: Create the Grid2Op environment
    print("🏗️  Creating Grid2Op environment...")
    try:
        env = grid2op.make(
            "l2rpn_case14_sandbox",
            backend=LightSimBackend()
        )
        print("✅ Environment created successfully!")
        print(f"   Environment: {env.name}")
        print(f"   Backend: {type(env.backend).__name__}")
        print()
        
    except Exception as e:
        print(f"❌ Error creating environment: {e}")
        return
    
    # Step 2: Instantiate the GreedyBaselineAgent
    print("🤖 Creating Greedy Baseline Agent...")
    try:
        agent = GreedyBaselineAgent(env.action_space)
        print(f"✅ Agent created: {agent.name}")
        print(f"   Action space: {type(env.action_space).__name__}")
        print()
        
    except Exception as e:
        print(f"❌ Error creating agent: {e}")
        return
    
    # Step 3: Create the Runner
    print("🏃 Setting up Runner...")
    try:
        runner = Runner(
            **env.get_params_for_runner(),
            agentClass=None,  # We provide an instance, not a class
            agentInstance=agent
        )
        print("✅ Runner created successfully!")
        print()
        
    except Exception as e:
        print(f"❌ Error creating runner: {e}")
        return
    
    # Step 4: Run the agent for 5 episodes
    print("🚀 Running agent for 5 episodes...")
    print("-" * 30)
    
    try:
        results = runner.run(nb_episode=5, pbar=True)
        
        print("\n📊 Simulation Results:")
        print("=" * 25)
        
        for i, episode_result in enumerate(results):
            episode_id, episode_name, cum_reward, nb_time_step, max_ts = episode_result[:5]
            
            print(f"\n📈 Episode {i+1}:")
            print(f"   • Episode ID: {episode_id}")
            print(f"   • Episode Name: {episode_name}")
            print(f"   • Cumulative Reward: {cum_reward:.2f}")
            print(f"   • Time Steps Survived: {nb_time_step}/{max_ts}")
            print(f"   • Survival Rate: {(nb_time_step/max_ts)*100:.1f}%")
            
            # Check if episode completed successfully
            if nb_time_step == max_ts:
                print(f"   ✅ Episode completed successfully!")
            else:
                print(f"   ⚠️  Episode ended early (possible game over)")
        
        # Calculate overall statistics
        total_timesteps = sum(result[3] for result in results)
        total_possible = sum(result[4] for result in results)
        avg_reward = np.mean([result[2] for result in results])
        
        print(f"\n🎯 Overall Performance:")
        print(f"   • Total Episodes: {len(results)}")
        print(f"   • Total Time Steps: {total_timesteps}/{total_possible}")
        print(f"   • Overall Survival Rate: {(total_timesteps/total_possible)*100:.1f}%")
        print(f"   • Average Reward per Episode: {avg_reward:.2f}")
        
        # Performance assessment
        survival_rate = (total_timesteps/total_possible)*100
        if survival_rate > 90:
            print(f"   🏆 Excellent performance!")
        elif survival_rate > 70:
            print(f"   👍 Good performance!")
        elif survival_rate > 50:
            print(f"   📈 Moderate performance - room for improvement")
        else:
            print(f"   📉 Poor performance - agent needs significant improvement")
            
    except Exception as e:
        print(f"❌ Error during simulation: {e}")
        return
    
    print(f"\n🎉 Simulation completed successfully!")
    print(f"💡 The greedy baseline agent provides a simple heuristic approach")
    print(f"   for managing power grid topology during line overloads.")


if __name__ == "__main__":
    main()
