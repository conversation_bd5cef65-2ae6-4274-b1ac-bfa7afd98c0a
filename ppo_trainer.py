#!/usr/bin/env python3
"""
PPO Trainer for Grid2Op Agents

This module implements Proximal Policy Optimization (PPO) training for
grid topology optimization agents using graph neural networks and
capsule networks for feature extraction.

Author: Grid Agent Project
Date: 2025-07-13
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import time
from collections import deque
import logging
from torch.utils.data import DataLoader, TensorDataset


class PPOReplayBuffer:
    """
    Replay buffer for storing PPO rollout experience.
    """
    
    def __init__(self, capacity: int, device: str = 'cpu'):
        """
        Initialize the replay buffer.
        
        Parameters
        ----------
        capacity : int
            Maximum number of experiences to store
        device : str
            Device to store tensors on
        """
        self.capacity = capacity
        self.device = device
        self.clear()
    
    def clear(self):
        """Clear all stored experiences."""
        self.states = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.dones = []
        self.values = []
        self.size = 0
    
    def store(self, state, action, log_prob, reward, done, value):
        """Store a single experience."""
        self.states.append(state)
        self.actions.append(action)
        self.log_probs.append(log_prob)
        self.rewards.append(reward)
        self.dones.append(done)
        self.values.append(value)
        self.size += 1
    
    def get_all(self):
        """Get all stored experiences as tensors."""
        return {
            'states': self.states,  # Keep as list of graph objects
            'actions': torch.tensor(self.actions, dtype=torch.long, device=self.device),
            'log_probs': torch.tensor(self.log_probs, dtype=torch.float32, device=self.device),
            'rewards': torch.tensor(self.rewards, dtype=torch.float32, device=self.device),
            'dones': torch.tensor(self.dones, dtype=torch.bool, device=self.device),
            'values': torch.tensor(self.values, dtype=torch.float32, device=self.device)
        }


def compute_gae_advantages(rewards: torch.Tensor, values: torch.Tensor, dones: torch.Tensor,
                          next_value: float, gamma: float = 0.99, gae_lambda: float = 0.95) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Compute Generalized Advantage Estimation (GAE) advantages and returns.
    
    Parameters
    ----------
    rewards : torch.Tensor
        Rewards for each timestep
    values : torch.Tensor
        Value estimates for each timestep
    dones : torch.Tensor
        Done flags for each timestep
    next_value : float
        Value estimate for the next state after the rollout
    gamma : float
        Discount factor
    gae_lambda : float
        GAE lambda parameter
        
    Returns
    -------
    Tuple[torch.Tensor, torch.Tensor]
        Advantages and returns
    """
    
    advantages = torch.zeros_like(rewards)
    returns = torch.zeros_like(rewards)
    
    gae = 0
    next_value = torch.tensor(next_value, device=rewards.device)
    
    # Iterate backwards through the rollout
    for t in reversed(range(len(rewards))):
        if t == len(rewards) - 1:
            next_non_terminal = 1.0 - dones[t].float()
            next_value_t = next_value
        else:
            next_non_terminal = 1.0 - dones[t].float()
            next_value_t = values[t + 1]
        
        # TD error
        delta = rewards[t] + gamma * next_value_t * next_non_terminal - values[t]
        
        # GAE calculation
        gae = delta + gamma * gae_lambda * next_non_terminal * gae
        advantages[t] = gae
        
        # Returns calculation
        returns[t] = advantages[t] + values[t]
    
    return advantages, returns


def train_agent(agent, env, converter, action_mapper, optimizer, hyperparameters: Dict, device: str = 'cpu'):
    """
    Main PPO training function for grid agents.
    
    Parameters
    ----------
    agent : torch.nn.Module
        The PPO agent with actor-critic architecture
    env : grid2op.Environment
        The Grid2Op environment
    converter : GraphObservationConverter
        Converter for observations to graph data
    action_mapper : ActionSpaceMapper
        Mapper for discrete actions to Grid2Op actions
    optimizer : torch.optim.Optimizer
        Optimizer for the agent
    hyperparameters : Dict
        Training hyperparameters
    device : str
        Device to run training on
    """
    
    # Extract hyperparameters
    total_timesteps = hyperparameters.get('total_timesteps', 1000000)
    n_steps = hyperparameters.get('n_steps', 2048)
    batch_size = hyperparameters.get('batch_size', 64)
    k_epochs = hyperparameters.get('k_epochs', 4)
    gamma = hyperparameters.get('gamma', 0.99)
    gae_lambda = hyperparameters.get('gae_lambda', 0.95)
    clip_epsilon = hyperparameters.get('clip_epsilon', 0.2)
    value_loss_coef = hyperparameters.get('value_loss_coef', 0.5)
    entropy_coef = hyperparameters.get('entropy_coef', 0.01)
    max_grad_norm = hyperparameters.get('max_grad_norm', 0.5)
    
    # Initialize replay buffer
    replay_buffer = PPOReplayBuffer(capacity=n_steps, device=device)
    
    # Tracking variables
    global_step = 0
    episode_rewards = deque(maxlen=100)
    episode_lengths = deque(maxlen=100)
    
    # Logging setup
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    print(f"🚀 Starting PPO Training")
    print(f"   • Total timesteps: {total_timesteps:,}")
    print(f"   • Rollout length: {n_steps}")
    print(f"   • Batch size: {batch_size}")
    print(f"   • Update epochs: {k_epochs}")
    print(f"   • Device: {device}")
    print("=" * 50)
    
    start_time = time.time()
    
    # Main training loop
    while global_step < total_timesteps:
        
        # Phase 1: Collect rollout experience
        print(f"📊 Collecting rollout {global_step // n_steps + 1}...")
        
        obs = env.reset()
        episode_reward = 0
        episode_length = 0
        
        for step in range(n_steps):
            # Convert observation to graph
            graph_data = converter.convert(obs)
            
            # Get action and value from agent
            with torch.no_grad():
                action_idx, log_prob, value = agent.get_action_and_value(graph_data)
            
            # Convert to Grid2Op action
            grid2op_action = action_mapper.map_to_grid2op_action(action_idx.item())
            
            # Step environment
            next_obs, reward, done, info = env.step(grid2op_action)
            
            # Store experience
            replay_buffer.store(
                state=graph_data,
                action=action_idx.item(),
                log_prob=log_prob.item(),
                reward=reward,
                done=done,
                value=value.item()
            )
            
            # Update tracking
            episode_reward += reward
            episode_length += 1
            global_step += 1
            
            # Handle episode end
            if done:
                episode_rewards.append(episode_reward)
                episode_lengths.append(episode_length)
                
                print(f"   Episode finished: reward={episode_reward:.2f}, length={episode_length}")
                
                obs = env.reset()
                episode_reward = 0
                episode_length = 0
            else:
                obs = next_obs
            
            if global_step >= total_timesteps:
                break
        
        # Phase 2: Calculate advantages using GAE
        print(f"🧮 Computing advantages...")
        
        # Get next value for bootstrapping
        if not done:
            next_graph = converter.convert(obs)
            with torch.no_grad():
                _, _, next_value = agent.get_action_and_value(next_graph)
                next_value = next_value.item()
        else:
            next_value = 0.0
        
        # Get all rollout data
        rollout_data = replay_buffer.get_all()
        
        # Compute GAE advantages and returns
        advantages, returns = compute_gae_advantages(
            rewards=rollout_data['rewards'],
            values=rollout_data['values'],
            dones=rollout_data['dones'],
            next_value=next_value,
            gamma=gamma,
            gae_lambda=gae_lambda
        )
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # Phase 3: Policy learning updates
        print(f"🎓 Performing policy updates...")
        
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        
        for epoch in range(k_epochs):
            # Create mini-batches
            indices = torch.randperm(n_steps, device=device)
            
            for start_idx in range(0, n_steps, batch_size):
                end_idx = min(start_idx + batch_size, n_steps)
                batch_indices = indices[start_idx:end_idx]
                
                # Get batch data
                batch_states = [rollout_data['states'][i] for i in batch_indices]
                batch_actions = rollout_data['actions'][batch_indices]
                batch_old_log_probs = rollout_data['log_probs'][batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                
                # Re-evaluate actions with current policy
                batch_new_log_probs, batch_new_values, batch_entropy = agent.evaluate_actions(
                    batch_states, batch_actions
                )
                
                # Calculate policy loss (PPO clipped objective)
                ratio = torch.exp(batch_new_log_probs - batch_old_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - clip_epsilon, 1 + clip_epsilon) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # Calculate value loss
                value_loss = F.mse_loss(batch_new_values.squeeze(), batch_returns)
                
                # Calculate entropy loss
                entropy_loss = -batch_entropy.mean()
                
                # Total loss
                total_loss = (policy_loss + 
                             value_loss_coef * value_loss + 
                             entropy_coef * entropy_loss)
                
                # Optimization step
                optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(agent.parameters(), max_grad_norm)
                optimizer.step()
                
                # Track losses
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
        
        # Clear replay buffer
        replay_buffer.clear()
        
        # Logging
        if len(episode_rewards) > 0:
            avg_reward = np.mean(episode_rewards)
            avg_length = np.mean(episode_lengths)
            
            elapsed_time = time.time() - start_time
            fps = global_step / elapsed_time
            
            print(f"📈 Update {global_step // n_steps}:")
            print(f"   • Global step: {global_step:,}/{total_timesteps:,}")
            print(f"   • Average reward: {avg_reward:.2f}")
            print(f"   • Average episode length: {avg_length:.1f}")
            print(f"   • Policy loss: {total_policy_loss / (k_epochs * (n_steps // batch_size)):.4f}")
            print(f"   • Value loss: {total_value_loss / (k_epochs * (n_steps // batch_size)):.4f}")
            print(f"   • Entropy loss: {total_entropy_loss / (k_epochs * (n_steps // batch_size)):.4f}")
            print(f"   • FPS: {fps:.1f}")
            print("-" * 50)
    
    print(f"🎉 Training completed!")
    print(f"   • Total time: {time.time() - start_time:.1f} seconds")
    print(f"   • Final average reward: {np.mean(episode_rewards):.2f}")


def create_default_hyperparameters() -> Dict:
    """
    Create default hyperparameters for PPO training.
    
    Returns
    -------
    Dict
        Default hyperparameters
    """
    return {
        'total_timesteps': 1000000,
        'n_steps': 2048,
        'batch_size': 64,
        'k_epochs': 4,
        'gamma': 0.99,
        'gae_lambda': 0.95,
        'clip_epsilon': 0.2,
        'value_loss_coef': 0.5,
        'entropy_coef': 0.01,
        'max_grad_norm': 0.5,
        'learning_rate': 3e-4
    }


if __name__ == "__main__":
    print("🔧 PPO Trainer Module")
    print("This module provides PPO training functionality for Grid2Op agents.")
    print("Import and use the train_agent() function with your agent and environment.")
    
    # Display default hyperparameters
    default_params = create_default_hyperparameters()
    print(f"\n📋 Default Hyperparameters:")
    for key, value in default_params.items():
        print(f"   • {key}: {value}")
