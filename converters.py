#!/usr/bin/env python3
"""
Data Conversion Utilities for Grid2Op to PyTorch Geometric

This module provides utilities to convert Grid2Op observations into PyTorch Geometric
graph data structures suitable for Graph Neural Networks (GNNs).

Author: Grid Agent Project
Date: 2025-07-13
"""

import torch
import torch_geometric
from torch_geometric.data import Data
import numpy as np
import networkx as nx
from typing import Dict, Tu<PERSON>, Optional
import warnings


class GraphObservationConverter:
    """
    Converts Grid2Op observations to PyTorch Geometric Data objects.
    
    This converter transforms power grid observations into graph representations
    where nodes represent buses and edges represent power lines. It includes
    proper normalization of features based on statistics computed from sample episodes.
    """
    
    def __init__(self, env, num_episodes: int = 2, device: str = 'mps'):
        """
        Initialize the converter and compute normalization statistics.
        
        Parameters
        ----------
        env : grid2op.Environment
            The Grid2Op environment instance
        num_episodes : int, default=2
            Number of episodes to use for computing normalization statistics
        device : str, default='mps'
            Target device for PyTorch tensors ('mps', 'cuda', 'cpu')
        """
        self.env = env
        self.device = device
        self.num_episodes = num_episodes
        
        # Initialize normalization statistics
        self.node_stats = {}  # Will store mean and std for node features
        self.edge_stats = {}  # Will store mean and std for edge features
        
        print(f"🔧 Initializing GraphObservationConverter...")
        print(f"   Device: {device}")
        print(f"   Computing normalization stats from {num_episodes} episodes...")
        
        # Compute normalization statistics
        self._compute_normalization_stats()
        
        print(f"✅ GraphObservationConverter initialized successfully!")
        
    def _compute_normalization_stats(self):
        """
        Compute normalization statistics (mean and std) for node and edge features
        by running through sample episodes.
        """
        
        # Collect features from multiple episodes
        node_features_list = []
        edge_features_list = []
        
        for episode in range(self.num_episodes):
            print(f"   📊 Processing episode {episode + 1}/{self.num_episodes}...")
            
            obs = self.env.reset()
            done = False
            step_count = 0
            max_steps = min(100, self.env.max_episode_duration())  # Limit steps per episode
            
            while not done and step_count < max_steps:
                try:
                    # Get the energy graph
                    graph = obs.get_energy_graph()
                    
                    if graph.number_of_nodes() > 0 and graph.number_of_edges() > 0:
                        # Extract node features
                        node_features = self._extract_node_features_raw(obs, graph)
                        if node_features is not None:
                            node_features_list.append(node_features)
                        
                        # Extract edge features
                        edge_features = self._extract_edge_features_raw(obs, graph)
                        if edge_features is not None:
                            edge_features_list.append(edge_features)
                    
                    # Take a random action to continue the episode
                    action = self.env.action_space.sample()
                    obs, reward, done, info = self.env.step(action)
                    step_count += 1
                    
                except Exception as e:
                    print(f"   ⚠️  Warning: Error in episode {episode + 1}, step {step_count}: {e}")
                    break
        
        # Compute statistics
        if node_features_list:
            all_node_features = np.vstack(node_features_list)
            self.node_stats['mean'] = np.mean(all_node_features, axis=0)
            self.node_stats['std'] = np.std(all_node_features, axis=0) + 1e-8  # Add small epsilon
            print(f"   📈 Node features: {all_node_features.shape[1]} dimensions")
        
        if edge_features_list:
            all_edge_features = np.vstack(edge_features_list)
            self.edge_stats['mean'] = np.mean(all_edge_features, axis=0)
            self.edge_stats['std'] = np.std(all_edge_features, axis=0) + 1e-8  # Add small epsilon
            print(f"   📈 Edge features: {all_edge_features.shape[1]} dimensions")
    
    def _extract_node_features_raw(self, observation, graph) -> Optional[np.ndarray]:
        """Extract raw node features for statistics computation."""
        try:
            node_features = []
            
            for node_id in graph.nodes():
                # Get node attributes from the graph
                node_data = graph.nodes[node_id]
                
                # Extract features: voltage magnitude, active power, reactive power
                v_mag = node_data.get('v', 0.0)  # Voltage magnitude
                p_inj = node_data.get('p', 0.0)  # Active power injection
                q_inj = node_data.get('q', 0.0)  # Reactive power injection
                
                node_features.append([v_mag, p_inj, q_inj])
            
            return np.array(node_features, dtype=np.float32)
            
        except Exception as e:
            warnings.warn(f"Error extracting node features: {e}")
            return None
    
    def _extract_edge_features_raw(self, observation, graph) -> Optional[np.ndarray]:
        """Extract raw edge features for statistics computation."""
        try:
            edge_features = []
            
            for edge in graph.edges():
                edge_data = graph.edges[edge]
                
                # Extract features: rho, active power flow, reactive power flow, cooldown
                rho = edge_data.get('rho', 0.0)  # Thermal loading
                p_flow = edge_data.get('p_or', 0.0)  # Active power flow (origin)
                q_flow = edge_data.get('q_or', 0.0)  # Reactive power flow (origin)
                cooldown = float(edge_data.get('cooldown', 0) > 0)  # Binary cooldown indicator
                
                edge_features.append([rho, p_flow, q_flow, cooldown])
            
            return np.array(edge_features, dtype=np.float32)
            
        except Exception as e:
            warnings.warn(f"Error extracting edge features: {e}")
            return None
    
    def convert(self, observation) -> Data:
        """
        Convert a Grid2Op observation to a PyTorch Geometric Data object.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            The Grid2Op observation to convert
            
        Returns
        -------
        torch_geometric.data.Data
            Graph data object with node features (x), edge indices (edge_index),
            and edge features (edge_attr)
        """
        
        try:
            # Step 1: Get the energy graph from the observation
            graph = observation.get_energy_graph()
            
            if graph.number_of_nodes() == 0:
                # Handle empty graph case (e.g., during game over)
                return self._create_empty_graph()
            
            # Step 2: Extract edge index from networkx graph
            edge_index = self._extract_edge_index(graph)
            
            # Step 3: Construct node feature tensor
            x = self._construct_node_features(observation, graph)
            
            # Step 4: Construct edge feature tensor
            edge_attr = self._construct_edge_features(observation, graph)
            
            # Step 5: Create PyTorch Geometric Data object
            data = Data(
                x=x,
                edge_index=edge_index,
                edge_attr=edge_attr
            )
            
            return data
            
        except Exception as e:
            print(f"❌ Error converting observation to graph: {e}")
            return self._create_empty_graph()
    
    def _extract_edge_index(self, graph) -> torch.Tensor:
        """Extract edge index tensor from networkx graph."""
        edges = list(graph.edges())
        if not edges:
            # Return empty edge index
            return torch.empty((2, 0), dtype=torch.long, device=self.device)
        
        # Convert to tensor format [2, num_edges]
        edge_index = torch.tensor(edges, dtype=torch.long, device=self.device).t().contiguous()
        return edge_index
    
    def _construct_node_features(self, observation, graph) -> torch.Tensor:
        """Construct normalized node feature tensor."""
        node_features = []
        
        for node_id in sorted(graph.nodes()):
            node_data = graph.nodes[node_id]
            
            # Extract raw features
            v_mag = node_data.get('v', 0.0)
            p_inj = node_data.get('p', 0.0)
            q_inj = node_data.get('q', 0.0)
            
            features = np.array([v_mag, p_inj, q_inj], dtype=np.float32)
            
            # Normalize features if statistics are available
            if self.node_stats:
                features = (features - self.node_stats['mean']) / self.node_stats['std']
            
            node_features.append(features)
        
        # Convert to tensor
        x = torch.tensor(np.array(node_features), dtype=torch.float32, device=self.device)
        return x
    
    def _construct_edge_features(self, observation, graph) -> torch.Tensor:
        """Construct normalized edge feature tensor."""
        edge_features = []
        
        for edge in graph.edges():
            edge_data = graph.edges[edge]
            
            # Extract raw features
            rho = edge_data.get('rho', 0.0)
            p_flow = edge_data.get('p_or', 0.0)
            q_flow = edge_data.get('q_or', 0.0)
            cooldown = float(edge_data.get('cooldown', 0) > 0)
            
            features = np.array([rho, p_flow, q_flow, cooldown], dtype=np.float32)
            
            # Normalize features if statistics are available (except binary cooldown)
            if self.edge_stats:
                normalized_features = (features - self.edge_stats['mean']) / self.edge_stats['std']
                # Keep cooldown as binary (don't normalize)
                normalized_features[3] = features[3]
                features = normalized_features
            
            edge_features.append(features)
        
        # Convert to tensor
        if edge_features:
            edge_attr = torch.tensor(np.array(edge_features), dtype=torch.float32, device=self.device)
        else:
            # Empty edge attributes
            edge_attr = torch.empty((0, 4), dtype=torch.float32, device=self.device)
        
        return edge_attr
    
    def _create_empty_graph(self) -> Data:
        """Create an empty graph for edge cases."""
        return Data(
            x=torch.empty((1, 3), dtype=torch.float32, device=self.device),
            edge_index=torch.empty((2, 0), dtype=torch.long, device=self.device),
            edge_attr=torch.empty((0, 4), dtype=torch.float32, device=self.device)
        )
    
    def get_feature_dimensions(self) -> Tuple[int, int]:
        """
        Get the dimensions of node and edge features.
        
        Returns
        -------
        tuple
            (node_feature_dim, edge_feature_dim)
        """
        return (3, 4)  # 3 node features, 4 edge features
