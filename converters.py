#!/usr/bin/env python3
"""
Data Conversion Utilities for Grid2Op to PyTorch Geometric

This module provides utilities to convert Grid2Op observations into PyTorch Geometric
graph data structures suitable for Graph Neural Networks (GNNs).

Author: Grid Agent Project
Date: 2025-07-13
"""

import torch
import torch_geometric
from torch_geometric.data import Data
import numpy as np
import networkx as nx
from typing import Dict, Tuple, Optional, List
import warnings
import itertools


class GraphObservationConverter:
    """
    Converts Grid2Op observations to PyTorch Geometric Data objects.
    
    This converter transforms power grid observations into graph representations
    where nodes represent buses and edges represent power lines. It includes
    proper normalization of features based on statistics computed from sample episodes.
    """
    
    def __init__(self, env, num_episodes: int = 2, device: str = 'mps'):
        """
        Initialize the converter and compute normalization statistics.
        
        Parameters
        ----------
        env : grid2op.Environment
            The Grid2Op environment instance
        num_episodes : int, default=2
            Number of episodes to use for computing normalization statistics
        device : str, default='mps'
            Target device for PyTorch tensors ('mps', 'cuda', 'cpu')
        """
        self.env = env
        self.device = device
        self.num_episodes = num_episodes
        
        # Initialize normalization statistics
        self.node_stats = {}  # Will store mean and std for node features
        self.edge_stats = {}  # Will store mean and std for edge features
        
        print(f"🔧 Initializing GraphObservationConverter...")
        print(f"   Device: {device}")
        print(f"   Computing normalization stats from {num_episodes} episodes...")
        
        # Compute normalization statistics
        self._compute_normalization_stats()
        
        print(f"✅ GraphObservationConverter initialized successfully!")
        
    def _compute_normalization_stats(self):
        """
        Compute normalization statistics (mean and std) for node and edge features
        by running through sample episodes.
        """
        
        # Collect features from multiple episodes
        node_features_list = []
        edge_features_list = []
        
        for episode in range(self.num_episodes):
            print(f"   📊 Processing episode {episode + 1}/{self.num_episodes}...")
            
            obs = self.env.reset()
            done = False
            step_count = 0
            max_steps = min(100, self.env.max_episode_duration())  # Limit steps per episode
            
            while not done and step_count < max_steps:
                try:
                    # Get the energy graph
                    graph = obs.get_energy_graph()
                    
                    if graph.number_of_nodes() > 0 and graph.number_of_edges() > 0:
                        # Extract node features
                        node_features = self._extract_node_features_raw(obs, graph)
                        if node_features is not None:
                            node_features_list.append(node_features)
                        
                        # Extract edge features
                        edge_features = self._extract_edge_features_raw(obs, graph)
                        if edge_features is not None:
                            edge_features_list.append(edge_features)
                    
                    # Take a random action to continue the episode
                    action = self.env.action_space.sample()
                    obs, reward, done, info = self.env.step(action)
                    step_count += 1
                    
                except Exception as e:
                    print(f"   ⚠️  Warning: Error in episode {episode + 1}, step {step_count}: {e}")
                    break
        
        # Compute statistics
        if node_features_list:
            all_node_features = np.vstack(node_features_list)
            self.node_stats['mean'] = np.mean(all_node_features, axis=0)
            self.node_stats['std'] = np.std(all_node_features, axis=0) + 1e-8  # Add small epsilon
            print(f"   📈 Node features: {all_node_features.shape[1]} dimensions")
        
        if edge_features_list:
            all_edge_features = np.vstack(edge_features_list)
            self.edge_stats['mean'] = np.mean(all_edge_features, axis=0)
            self.edge_stats['std'] = np.std(all_edge_features, axis=0) + 1e-8  # Add small epsilon
            print(f"   📈 Edge features: {all_edge_features.shape[1]} dimensions")
    
    def _extract_node_features_raw(self, observation, graph) -> Optional[np.ndarray]:
        """Extract raw node features for statistics computation."""
        try:
            node_features = []
            
            for node_id in graph.nodes():
                # Get node attributes from the graph
                node_data = graph.nodes[node_id]
                
                # Extract features: voltage magnitude, active power, reactive power
                v_mag = node_data.get('v', 0.0)  # Voltage magnitude
                p_inj = node_data.get('p', 0.0)  # Active power injection
                q_inj = node_data.get('q', 0.0)  # Reactive power injection
                
                node_features.append([v_mag, p_inj, q_inj])
            
            return np.array(node_features, dtype=np.float32)
            
        except Exception as e:
            warnings.warn(f"Error extracting node features: {e}")
            return None
    
    def _extract_edge_features_raw(self, observation, graph) -> Optional[np.ndarray]:
        """Extract raw edge features for statistics computation."""
        try:
            edge_features = []
            
            for edge in graph.edges():
                edge_data = graph.edges[edge]
                
                # Extract features: rho, active power flow, reactive power flow, cooldown
                rho = edge_data.get('rho', 0.0)  # Thermal loading
                p_flow = edge_data.get('p_or', 0.0)  # Active power flow (origin)
                q_flow = edge_data.get('q_or', 0.0)  # Reactive power flow (origin)
                cooldown = float(edge_data.get('cooldown', 0) > 0)  # Binary cooldown indicator
                
                edge_features.append([rho, p_flow, q_flow, cooldown])
            
            return np.array(edge_features, dtype=np.float32)
            
        except Exception as e:
            warnings.warn(f"Error extracting edge features: {e}")
            return None
    
    def convert(self, observation) -> Data:
        """
        Convert a Grid2Op observation to a PyTorch Geometric Data object.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            The Grid2Op observation to convert
            
        Returns
        -------
        torch_geometric.data.Data
            Graph data object with node features (x), edge indices (edge_index),
            and edge features (edge_attr)
        """
        
        try:
            # Step 1: Get the energy graph from the observation
            graph = observation.get_energy_graph()
            
            if graph.number_of_nodes() == 0:
                # Handle empty graph case (e.g., during game over)
                return self._create_empty_graph()
            
            # Step 2: Extract edge index from networkx graph
            edge_index = self._extract_edge_index(graph)
            
            # Step 3: Construct node feature tensor
            x = self._construct_node_features(observation, graph)
            
            # Step 4: Construct edge feature tensor
            edge_attr = self._construct_edge_features(observation, graph)
            
            # Step 5: Create PyTorch Geometric Data object
            data = Data(
                x=x,
                edge_index=edge_index,
                edge_attr=edge_attr
            )
            
            return data
            
        except Exception as e:
            print(f"❌ Error converting observation to graph: {e}")
            return self._create_empty_graph()
    
    def _extract_edge_index(self, graph) -> torch.Tensor:
        """Extract edge index tensor from networkx graph."""
        edges = list(graph.edges())
        if not edges:
            # Return empty edge index
            return torch.empty((2, 0), dtype=torch.long, device=self.device)
        
        # Convert to tensor format [2, num_edges]
        edge_index = torch.tensor(edges, dtype=torch.long, device=self.device).t().contiguous()
        return edge_index
    
    def _construct_node_features(self, observation, graph) -> torch.Tensor:
        """Construct normalized node feature tensor."""
        node_features = []
        
        for node_id in sorted(graph.nodes()):
            node_data = graph.nodes[node_id]
            
            # Extract raw features
            v_mag = node_data.get('v', 0.0)
            p_inj = node_data.get('p', 0.0)
            q_inj = node_data.get('q', 0.0)
            
            features = np.array([v_mag, p_inj, q_inj], dtype=np.float32)
            
            # Normalize features if statistics are available
            if self.node_stats:
                features = (features - self.node_stats['mean']) / self.node_stats['std']
            
            node_features.append(features)
        
        # Convert to tensor
        x = torch.tensor(np.array(node_features), dtype=torch.float32, device=self.device)
        return x
    
    def _construct_edge_features(self, observation, graph) -> torch.Tensor:
        """Construct normalized edge feature tensor."""
        edge_features = []
        
        for edge in graph.edges():
            edge_data = graph.edges[edge]
            
            # Extract raw features
            rho = edge_data.get('rho', 0.0)
            p_flow = edge_data.get('p_or', 0.0)
            q_flow = edge_data.get('q_or', 0.0)
            cooldown = float(edge_data.get('cooldown', 0) > 0)
            
            features = np.array([rho, p_flow, q_flow, cooldown], dtype=np.float32)
            
            # Normalize features if statistics are available (except binary cooldown)
            if self.edge_stats:
                normalized_features = (features - self.edge_stats['mean']) / self.edge_stats['std']
                # Keep cooldown as binary (don't normalize)
                normalized_features[3] = features[3]
                features = normalized_features
            
            edge_features.append(features)
        
        # Convert to tensor
        if edge_features:
            edge_attr = torch.tensor(np.array(edge_features), dtype=torch.float32, device=self.device)
        else:
            # Empty edge attributes
            edge_attr = torch.empty((0, 4), dtype=torch.float32, device=self.device)
        
        return edge_attr
    
    def _create_empty_graph(self) -> Data:
        """Create an empty graph for edge cases."""
        return Data(
            x=torch.empty((1, 3), dtype=torch.float32, device=self.device),
            edge_index=torch.empty((2, 0), dtype=torch.long, device=self.device),
            edge_attr=torch.empty((0, 4), dtype=torch.float32, device=self.device)
        )
    
    def get_feature_dimensions(self) -> Tuple[int, int]:
        """
        Get the dimensions of node and edge features.

        Returns
        -------
        tuple
            (node_feature_dim, edge_feature_dim)
        """
        return (3, 4)  # 3 node features, 4 edge features


class ActionSpaceMapper:
    """
    Maps discrete agent actions to Grid2Op topology actions.

    This class creates a discrete action space by enumerating all possible
    topology configurations for each substation. It provides a mapping from
    integer indices to valid Grid2Op actions, enabling the use of discrete
    action spaces with reinforcement learning algorithms.
    """

    def __init__(self, env):
        """
        Initialize the action space mapper.

        Parameters
        ----------
        env : grid2op.Environment
            The Grid2Op environment instance
        """
        self.env = env
        self.action_space = env.action_space

        print(f"🎯 Initializing ActionSpaceMapper...")
        print(f"   Grid: {env.name}")
        print(f"   Substations: {env.n_sub}")

        # Initialize action mapping
        self.action_map = []
        self._create_action_mapping()

        # Set action space size
        self.action_space_size = len(self.action_map)

        print(f"✅ ActionSpaceMapper initialized successfully!")
        print(f"   Total actions: {self.action_space_size}")

    def _create_action_mapping(self):
        """
        Create the mapping from action indices to Grid2Op action dictionaries.

        This method enumerates all possible topology configurations for each
        substation and creates a discrete action space.
        """

        # Action 0: Do nothing
        self.action_map.append({})
        print(f"   📋 Action 0: Do nothing")

        action_count = 1

        # Iterate through all substations
        for sub_id in range(self.env.n_sub):
            num_elements = self.env.sub_info[sub_id]

            # Skip substations with only 1 or 2 elements (no meaningful topology changes)
            if num_elements <= 2:
                continue

            print(f"   🔧 Processing substation {sub_id} ({num_elements} elements)...")

            # Generate all valid topology configurations for this substation
            valid_topologies = self._generate_valid_topologies(sub_id, num_elements)

            for topo in valid_topologies:
                action_dict = {
                    "set_bus": {
                        "substations_id": [(sub_id, topo)]
                    }
                }
                self.action_map.append(action_dict)
                action_count += 1

            print(f"      Added {len(valid_topologies)} topology actions for substation {sub_id}")

    def _generate_valid_topologies(self, sub_id: int, num_elements: int) -> List[np.ndarray]:
        """
        Generate all valid topology configurations for a substation.

        Parameters
        ----------
        sub_id : int
            Substation ID
        num_elements : int
            Number of elements in the substation

        Returns
        -------
        List[np.ndarray]
            List of valid topology configurations
        """
        valid_topologies = []

        # Get powerline positions for this substation
        powerlines_or_pos = self.env.line_or_to_sub_pos[
            self.env.line_or_to_subid == sub_id
        ]
        powerlines_ex_pos = self.env.line_ex_to_sub_pos[
            self.env.line_ex_to_subid == sub_id
        ]
        powerlines_pos = np.concatenate((powerlines_or_pos, powerlines_ex_pos))

        # Generate all possible 2-bus configurations
        # We use itertools.product to generate all combinations
        S = [0, 1]  # Binary choices for each element (except first)

        for tup in itertools.product(S, repeat=num_elements - 1):
            # Create topology vector
            indx = np.full(shape=num_elements, fill_value=False, dtype=bool)
            tup = np.array((0, *tup)).astype(bool)  # Add zero to first element (break symmetry)
            indx[tup] = True

            # Check if configuration is valid
            if self._is_valid_topology(indx, powerlines_pos, num_elements):
                # Create topology vector (1 for bus 1, 2 for bus 2)
                new_topo = np.full(shape=num_elements, fill_value=1, dtype=int)
                new_topo[~indx] = 2
                valid_topologies.append(new_topo)

        return valid_topologies

    def _is_valid_topology(self, indx: np.ndarray, powerlines_pos: np.ndarray, num_elements: int) -> bool:
        """
        Check if a topology configuration is valid.

        A topology is valid if:
        1. Each bus has at least 2 elements (almost always, except when a powerline is alone)
        2. Each bus has at least one powerline connection

        Parameters
        ----------
        indx : np.ndarray
            Boolean array indicating which elements go to bus 2 (False = bus 1, True = bus 2)
        powerlines_pos : np.ndarray
            Positions of powerlines in the substation
        num_elements : int
            Total number of elements in the substation

        Returns
        -------
        bool
            True if topology is valid, False otherwise
        """

        # Check if each bus has at least 2 elements
        bus1_count = (~indx).sum()
        bus2_count = indx.sum()

        if bus1_count < 2 or bus2_count < 2:
            return False

        # Check if each bus has at least one powerline
        if len(powerlines_pos) > 0:
            bus1_has_powerline = (~indx[powerlines_pos]).sum() > 0
            bus2_has_powerline = indx[powerlines_pos].sum() > 0

            if not (bus1_has_powerline and bus2_has_powerline):
                return False

        return True

    def map_to_grid2op_action(self, agent_action_index: int):
        """
        Convert an agent action index to a valid Grid2Op action.

        Parameters
        ----------
        agent_action_index : int
            The discrete action index chosen by the agent

        Returns
        -------
        grid2op.Action.BaseAction
            A valid Grid2Op action object
        """

        # Validate action index
        if not (0 <= agent_action_index < self.action_space_size):
            raise ValueError(
                f"Invalid action index {agent_action_index}. "
                f"Must be between 0 and {self.action_space_size - 1}"
            )

        # Get action dictionary
        action_dict = self.action_map[agent_action_index]

        # Convert to Grid2Op action
        try:
            grid2op_action = self.action_space(action_dict)
            return grid2op_action

        except Exception as e:
            print(f"❌ Error creating Grid2Op action for index {agent_action_index}: {e}")
            # Fallback to do-nothing action
            return self.action_space()

    def get_action_description(self, agent_action_index: int) -> str:
        """
        Get a human-readable description of an action.

        Parameters
        ----------
        agent_action_index : int
            The action index

        Returns
        -------
        str
            Description of the action
        """

        if agent_action_index == 0:
            return "Do nothing"

        if not (0 <= agent_action_index < self.action_space_size):
            return f"Invalid action index {agent_action_index}"

        action_dict = self.action_map[agent_action_index]

        if "set_bus" in action_dict and "substations_id" in action_dict["set_bus"]:
            sub_id, topo = action_dict["set_bus"]["substations_id"][0]
            bus1_elements = np.sum(topo == 1)
            bus2_elements = np.sum(topo == 2)
            return f"Substation {sub_id}: {bus1_elements} elements on bus 1, {bus2_elements} elements on bus 2"

        return f"Action {agent_action_index}: {action_dict}"

    def get_action_space_info(self) -> Dict:
        """
        Get information about the action space.

        Returns
        -------
        dict
            Dictionary containing action space information
        """

        substation_actions = {}
        for action_dict in self.action_map[1:]:  # Skip do-nothing action
            if "set_bus" in action_dict and "substations_id" in action_dict["set_bus"]:
                sub_id = action_dict["set_bus"]["substations_id"][0][0]
                if sub_id not in substation_actions:
                    substation_actions[sub_id] = 0
                substation_actions[sub_id] += 1

        return {
            "total_actions": self.action_space_size,
            "do_nothing_actions": 1,
            "topology_actions": self.action_space_size - 1,
            "substations_with_actions": len(substation_actions),
            "actions_per_substation": substation_actions
        }
