#!/usr/bin/env python3
"""
Test Different Grid2Op Environments for Stability

This script tests various Grid2Op environments to find stable ones
suitable for agent training.

Author: Grid Agent Project
Date: 2025-07-13
"""

import grid2op
from lightsim2grid import LightSimBackend
from custom_rewards import StabilityReward
import numpy as np


def test_environment_stability(env_name, num_episodes=3, max_steps=50):
    """
    Test an environment for stability with do-nothing actions.
    
    Parameters
    ----------
    env_name : str
        Name of the Grid2Op environment
    num_episodes : int
        Number of episodes to test
    max_steps : int
        Maximum steps per episode
        
    Returns
    -------
    dict
        Results summary
    """
    
    print(f"\n🧪 Testing Environment: {env_name}")
    print("-" * 50)
    
    try:
        # Try to create environment
        env = grid2op.make(env_name, backend=LightSimBackend())
        print(f"✅ Environment created successfully")
        print(f"   • Grid size: {env.n_line} lines, {env.n_sub} substations")
        print(f"   • Generators: {env.n_gen}, Loads: {env.n_load}")
        
        episode_lengths = []
        episode_rewards = []
        errors_count = 0
        
        for episode in range(num_episodes):
            try:
                obs = env.reset()
                episode_reward = 0
                step_count = 0
                
                print(f"   Episode {episode + 1}: Initial max_rho = {obs.rho.max():.3f}")
                
                for step in range(max_steps):
                    # Do nothing action
                    action = env.action_space()
                    obs, reward, done, info = env.step(action)
                    
                    episode_reward += reward
                    step_count += 1
                    
                    if done:
                        if info.get('exception', None) is not None:
                            errors_count += 1
                            print(f"     ❌ Error at step {step}: {info['exception']}")
                        break
                
                episode_lengths.append(step_count)
                episode_rewards.append(episode_reward)
                
                print(f"     Episode {episode + 1}: {step_count} steps, reward: {episode_reward:.2f}")
                
            except Exception as e:
                print(f"     ❌ Episode {episode + 1} failed: {e}")
                errors_count += 1
                episode_lengths.append(0)
                episode_rewards.append(-1000)
        
        env.close()
        
        # Calculate statistics
        avg_length = np.mean(episode_lengths)
        avg_reward = np.mean(episode_rewards)
        success_rate = (num_episodes - errors_count) / num_episodes
        
        result = {
            'env_name': env_name,
            'success': True,
            'avg_length': avg_length,
            'avg_reward': avg_reward,
            'success_rate': success_rate,
            'errors_count': errors_count,
            'grid_size': f"{env.n_line} lines, {env.n_sub} subs"
        }
        
        print(f"📊 Results: Avg length: {avg_length:.1f}, Success rate: {success_rate:.1%}")
        
        return result
        
    except Exception as e:
        print(f"❌ Failed to create environment: {e}")
        return {
            'env_name': env_name,
            'success': False,
            'error': str(e)
        }


def main():
    """Test multiple Grid2Op environments."""
    
    print("🔬 Grid2Op Environment Stability Testing")
    print("=" * 60)
    
    # List of environments to test
    environments_to_test = [
        "l2rpn_case14_sandbox",
        "l2rpn_neurips_2020_track1_small",
        "l2rpn_neurips_2020_track2_small", 
        "l2rpn_wcci_2022",
        "rte_case14_realistic",
        "rte_case5_example",
        "rte_case14_redisp",
        "l2rpn_idf_2023",
        "educ_case14_storage",
        "educ_case14_redisp"
    ]
    
    results = []
    
    for env_name in environments_to_test:
        try:
            result = test_environment_stability(env_name, num_episodes=3, max_steps=50)
            results.append(result)
        except KeyboardInterrupt:
            print("\n⏹️  Testing interrupted by user")
            break
        except Exception as e:
            print(f"❌ Unexpected error testing {env_name}: {e}")
            results.append({
                'env_name': env_name,
                'success': False,
                'error': str(e)
            })
    
    # Summary
    print(f"\n📋 Environment Stability Summary")
    print("=" * 60)
    
    stable_envs = []
    unstable_envs = []
    
    for result in results:
        if result['success']:
            if result['success_rate'] > 0.8 and result['avg_length'] > 20:
                stable_envs.append(result)
                status = "✅ STABLE"
            elif result['success_rate'] > 0.5:
                status = "⚠️  MODERATE"
            else:
                unstable_envs.append(result)
                status = "❌ UNSTABLE"
            
            print(f"{status} {result['env_name']}")
            print(f"   • Avg length: {result['avg_length']:.1f} steps")
            print(f"   • Success rate: {result['success_rate']:.1%}")
            print(f"   • Grid size: {result.get('grid_size', 'Unknown')}")
        else:
            unstable_envs.append(result)
            print(f"❌ FAILED {result['env_name']}: {result.get('error', 'Unknown error')}")
    
    print(f"\n🎯 Recommendations:")
    if stable_envs:
        print(f"✅ Use these stable environments for training:")
        for env in stable_envs[:3]:  # Top 3
            print(f"   • {env['env_name']} (avg length: {env['avg_length']:.1f})")
    else:
        print(f"⚠️  No fully stable environments found.")
        print(f"   Consider using a different Grid2Op version or custom chronics.")
    
    print(f"\n💡 Next Steps:")
    if stable_envs:
        best_env = max(stable_envs, key=lambda x: x['avg_length'])
        print(f"1. Use '{best_env['env_name']}' for training")
        print(f"2. Update main.py to use this environment")
        print(f"3. Re-run training with stable environment")
    else:
        print(f"1. Try creating custom chronics with stable power flow")
        print(f"2. Use a different backend (PandaPowerBackend)")
        print(f"3. Reduce the complexity of the reward function")


if __name__ == "__main__":
    main()
