#!/usr/bin/env python3
"""
Graph Federated Learning Agent Models

This module contains neural network models for grid agent training,
including capsule networks and graph neural network architectures
for power grid topology optimization.

Author: Grid Agent Project
Date: 2025-07-13
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional
import torch_geometric
from torch_geometric.nn import GCNConv
from torch_geometric.data import Data, Batch


class CapsuleLayer(nn.Module):
    """
    Capsule Layer with Dynamic Routing Algorithm.
    
    This layer implements the capsule network architecture with dynamic routing
    between capsules. Capsules are groups of neurons that represent the instantiation
    parameters of specific entities, providing better representation of hierarchical
    relationships in data.
    
    The dynamic routing algorithm iteratively refines the routing weights between
    input and output capsules based on the agreement between prediction vectors.
    """
    
    def __init__(
        self,
        num_input_capsules: int,
        num_output_capsules: int,
        input_capsule_dim: int,
        output_capsule_dim: int,
        num_routing_iterations: int = 3
    ):
        """
        Initialize the Capsule Layer.
        
        Parameters
        ----------
        num_input_capsules : int
            Number of input capsules
        num_output_capsules : int
            Number of output capsules
        input_capsule_dim : int
            Dimension of each input capsule
        output_capsule_dim : int
            Dimension of each output capsule
        num_routing_iterations : int, default=3
            Number of iterations for the dynamic routing algorithm
        """
        super(CapsuleLayer, self).__init__()
        
        self.num_input_capsules = num_input_capsules
        self.num_output_capsules = num_output_capsules
        self.input_capsule_dim = input_capsule_dim
        self.output_capsule_dim = output_capsule_dim
        self.num_routing_iterations = num_routing_iterations
        
        # Weight matrix for affine transformations
        # Shape: [1, num_input_capsules, num_output_capsules, output_capsule_dim, input_capsule_dim]
        self.W = nn.Parameter(
            torch.randn(
                1,
                num_input_capsules,
                num_output_capsules,
                output_capsule_dim,
                input_capsule_dim
            ) * 0.01  # Small initialization for stability
        )
        
        print(f"🔗 CapsuleLayer initialized:")
        print(f"   • Input capsules: {num_input_capsules} × {input_capsule_dim}D")
        print(f"   • Output capsules: {num_output_capsules} × {output_capsule_dim}D")
        print(f"   • Routing iterations: {num_routing_iterations}")
        print(f"   • Weight matrix shape: {self.W.shape}")
    
    def forward(self, u: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the capsule layer with dynamic routing.
        
        Parameters
        ----------
        u : torch.Tensor
            Input tensor of shape [batch_size, num_input_capsules, input_capsule_dim]
            
        Returns
        -------
        torch.Tensor
            Output capsules of shape [batch_size, num_output_capsules, output_capsule_dim]
        """
        
        batch_size = u.size(0)
        
        # Step 1: Compute prediction vectors u_hat
        # u: [batch_size, num_input_capsules, input_capsule_dim]
        # W: [1, num_input_capsules, num_output_capsules, output_capsule_dim, input_capsule_dim]
        # u_hat: [batch_size, num_input_capsules, num_output_capsules, output_capsule_dim]

        # Remove the first dimension from W and use einsum for the transformation
        W = self.W.squeeze(0)  # [num_input_capsules, num_output_capsules, output_capsule_dim, input_capsule_dim]

        # Use einsum to compute u_hat: W @ u for each input capsule and output capsule
        # 'ijkl,bil->bijk' means: for each batch b, input capsule i, multiply W[i,j,k,l] with u[b,i,l] to get u_hat[b,i,j,k]
        u_hat = torch.einsum('ijkl,bil->bijk', W, u)
        
        # Step 2: Initialize routing logits
        # b: [batch_size, num_input_capsules, num_output_capsules, 1]
        b = torch.zeros(
            batch_size,
            self.num_input_capsules,
            self.num_output_capsules,
            1,
            device=u.device,
            dtype=u.dtype
        )
        
        # Step 3: Dynamic routing algorithm
        for iteration in range(self.num_routing_iterations):
            
            # Step 3i: Calculate routing weights using softmax
            # c: [batch_size, num_input_capsules, num_output_capsules, 1]
            c = F.softmax(b, dim=2)  # Softmax over num_output_capsules dimension
            
            # Step 3ii: Compute weighted sum of prediction vectors
            # s: [batch_size, num_output_capsules, output_capsule_dim]
            s = torch.sum(c * u_hat, dim=1)  # Sum over num_input_capsules dimension
            
            # Step 3iii: Apply squashing non-linearity
            # v: [batch_size, num_output_capsules, output_capsule_dim]
            v = self.squash(s)
            
            # Step 3iv: Update routing logits (except for last iteration)
            if iteration < self.num_routing_iterations - 1:
                # Expand v for agreement calculation
                # v_expanded: [batch_size, 1, num_output_capsules, output_capsule_dim]
                v_expanded = v.unsqueeze(1)
                
                # Calculate agreement between u_hat and v
                # agreement: [batch_size, num_input_capsules, num_output_capsules, 1]
                agreement = torch.sum(u_hat * v_expanded, dim=-1, keepdim=True)
                
                # Update routing logits
                b = b + agreement
        
        return v
    
    def squash(self, s: torch.Tensor) -> torch.Tensor:
        """
        Apply the squashing non-linearity to ensure capsule outputs have unit length.
        
        The squashing function is defined as:
        v = (s_norm_sq / (1 + s_norm_sq)) * (s / s_norm)
        
        where s_norm is the L2 norm of s and s_norm_sq is its square.
        
        Parameters
        ----------
        s : torch.Tensor
            Input tensor to be squashed
            
        Returns
        -------
        torch.Tensor
            Squashed tensor with the same shape as input
        """
        
        # Calculate L2 norm squared
        # s_norm_sq: [batch_size, num_output_capsules, 1]
        s_norm_sq = torch.sum(s ** 2, dim=-1, keepdim=True)
        
        # Calculate L2 norm (add small epsilon for numerical stability)
        # s_norm: [batch_size, num_output_capsules, 1]
        s_norm = torch.sqrt(s_norm_sq + 1e-8)
        
        # Apply squashing function
        # Scale factor: s_norm_sq / (1 + s_norm_sq)
        scale_factor = s_norm_sq / (1.0 + s_norm_sq)
        
        # Unit vector: s / s_norm
        unit_vector = s / s_norm
        
        # Final squashed output
        v = scale_factor * unit_vector
        
        return v
    
    def extra_repr(self) -> str:
        """
        Extra representation for debugging and model summary.
        
        Returns
        -------
        str
            String representation of the layer parameters
        """
        return (
            f"num_input_capsules={self.num_input_capsules}, "
            f"num_output_capsules={self.num_output_capsules}, "
            f"input_capsule_dim={self.input_capsule_dim}, "
            f"output_capsule_dim={self.output_capsule_dim}, "
            f"num_routing_iterations={self.num_routing_iterations}"
        )


class PrimaryCapsuleLayer(nn.Module):
    """
    Primary Capsule Layer for converting convolutional features to capsules.
    
    This layer typically follows convolutional layers and creates the first
    level of capsules from feature maps.
    """
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        capsule_dim: int,
        kernel_size: int = 9,
        stride: int = 2
    ):
        """
        Initialize the Primary Capsule Layer.
        
        Parameters
        ----------
        in_channels : int
            Number of input channels
        out_channels : int
            Number of output channels (will be reshaped to capsules)
        capsule_dim : int
            Dimension of each capsule
        kernel_size : int, default=9
            Convolution kernel size
        stride : int, default=2
            Convolution stride
        """
        super(PrimaryCapsuleLayer, self).__init__()
        
        self.capsule_dim = capsule_dim
        self.num_capsules = out_channels // capsule_dim
        
        # Convolutional layer to generate capsule inputs
        self.conv = nn.Conv2d(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=kernel_size,
            stride=stride
        )
        
        print(f"🔗 PrimaryCapsuleLayer initialized:")
        print(f"   • Input channels: {in_channels}")
        print(f"   • Output capsules: {self.num_capsules} × {capsule_dim}D")
        print(f"   • Kernel size: {kernel_size}, Stride: {stride}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the primary capsule layer.
        
        Parameters
        ----------
        x : torch.Tensor
            Input tensor from convolutional layers
            
        Returns
        -------
        torch.Tensor
            Output capsules
        """
        
        # Apply convolution
        conv_out = self.conv(x)
        
        # Reshape to capsules and apply squashing
        batch_size = conv_out.size(0)
        
        # Reshape: [batch_size, out_channels, height, width] 
        # -> [batch_size, num_capsules, capsule_dim, height, width]
        capsules = conv_out.view(
            batch_size,
            self.num_capsules,
            self.capsule_dim,
            conv_out.size(2),
            conv_out.size(3)
        )
        
        # Flatten spatial dimensions and transpose
        # -> [batch_size, num_capsules * height * width, capsule_dim]
        capsules = capsules.view(batch_size, self.num_capsules, -1).transpose(1, 2)
        capsules = capsules.contiguous().view(batch_size, -1, self.capsule_dim)
        
        # Apply squashing non-linearity
        return self.squash(capsules)
    
    def squash(self, s: torch.Tensor) -> torch.Tensor:
        """Apply squashing non-linearity (same as CapsuleLayer)."""
        s_norm_sq = torch.sum(s ** 2, dim=-1, keepdim=True)
        s_norm = torch.sqrt(s_norm_sq + 1e-8)
        scale_factor = s_norm_sq / (1.0 + s_norm_sq)
        unit_vector = s / s_norm
        return scale_factor * unit_vector


class GraphCapsuleExtractor(nn.Module):
    """
    Graph Capsule Extractor combining GCN and Capsule Networks.

    This class integrates Graph Convolutional Networks (GCNs) with Capsule Networks
    to extract hierarchical graph-level features. The GCN layers learn context-aware
    node embeddings, which are then processed by capsule layers to capture part-whole
    relationships and produce high-level graph representations.
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        num_gcn_layers: int = 3,
        num_input_capsules: int = None,
        num_output_capsules: int = 10,
        input_capsule_dim: int = None,
        output_capsule_dim: int = 16,
        num_routing_iterations: int = 3,
        dropout: float = 0.1
    ):
        """
        Initialize the Graph Capsule Extractor.

        Parameters
        ----------
        input_dim : int
            Dimension of input node features
        hidden_dim : int
            Hidden dimension for GCN layers
        num_gcn_layers : int, default=3
            Number of GCN layers
        num_input_capsules : int, optional
            Number of input capsules (if None, will be set to hidden_dim // input_capsule_dim)
        num_output_capsules : int, default=10
            Number of output capsules
        input_capsule_dim : int, optional
            Dimension of input capsules (if None, will be set to hidden_dim // 4)
        output_capsule_dim : int, default=16
            Dimension of output capsules
        num_routing_iterations : int, default=3
            Number of routing iterations for capsule layer
        dropout : float, default=0.1
            Dropout rate for GCN layers
        """
        super(GraphCapsuleExtractor, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_gcn_layers = num_gcn_layers
        self.dropout = dropout

        # Set default capsule dimensions if not provided
        if input_capsule_dim is None:
            input_capsule_dim = max(4, hidden_dim // 4)  # Ensure reasonable capsule dimension
        if num_input_capsules is None:
            num_input_capsules = hidden_dim // input_capsule_dim

        self.input_capsule_dim = input_capsule_dim
        self.num_input_capsules = num_input_capsules
        self.num_output_capsules = num_output_capsules
        self.output_capsule_dim = output_capsule_dim

        # Create GCN layers
        self.gcn_layers = nn.ModuleList()

        # First GCN layer
        self.gcn_layers.append(GCNConv(input_dim, hidden_dim))

        # Hidden GCN layers
        for _ in range(num_gcn_layers - 1):
            self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))

        # Projection layer to match capsule input requirements
        capsule_input_dim = num_input_capsules * input_capsule_dim
        self.projection = nn.Linear(hidden_dim, capsule_input_dim)

        # Capsule layer
        self.capsule_layer = CapsuleLayer(
            num_input_capsules=num_input_capsules,
            num_output_capsules=num_output_capsules,
            input_capsule_dim=input_capsule_dim,
            output_capsule_dim=output_capsule_dim,
            num_routing_iterations=num_routing_iterations
        )

        # Output dimension
        self.output_dim = num_output_capsules * output_capsule_dim

        print(f"🔗 GraphCapsuleExtractor initialized:")
        print(f"   • Input dim: {input_dim}")
        print(f"   • Hidden dim: {hidden_dim}")
        print(f"   • GCN layers: {num_gcn_layers}")
        print(f"   • Primary capsules: {num_input_capsules} × {input_capsule_dim}D")
        print(f"   • Output capsules: {num_output_capsules} × {output_capsule_dim}D")
        print(f"   • Final output dim: {self.output_dim}")

    def forward(self, data) -> torch.Tensor:
        """
        Forward pass through the Graph Capsule Extractor.

        Parameters
        ----------
        data : torch_geometric.data.Data or torch_geometric.data.Batch
            Graph data containing node features (x) and edge indices (edge_index)

        Returns
        -------
        torch.Tensor
            Flattened graph-level features of shape [batch_size, output_dim]
        """

        x, edge_index = data.x, data.edge_index
        batch = getattr(data, 'batch', None)

        # Step 1: Pass through GCN layers
        for i, gcn_layer in enumerate(self.gcn_layers):
            x = gcn_layer(x, edge_index)

            # Apply ReLU activation (except for the last layer)
            if i < len(self.gcn_layers) - 1:
                x = F.relu(x)
                x = F.dropout(x, p=self.dropout, training=self.training)

        # Step 2: Global pooling to get graph-level representations
        if batch is not None:
            # Handle batched graphs
            try:
                from torch_geometric.nn import global_mean_pool
                x = global_mean_pool(x, batch)  # [batch_size, hidden_dim]
            except RuntimeError as e:
                if "MPS" in str(e) or "Placeholder storage" in str(e):
                    # Fallback for MPS device issues
                    print(f"⚠️  MPS pooling issue, using manual pooling: {e}")
                    # Manual global mean pooling
                    batch_size = batch.max().item() + 1
                    pooled = []
                    for i in range(batch_size):
                        mask = (batch == i)
                        if mask.sum() > 0:
                            graph_features = x[mask].mean(dim=0)
                        else:
                            graph_features = torch.zeros(x.size(1), device=x.device)
                        pooled.append(graph_features)
                    x = torch.stack(pooled)
                else:
                    raise e
        else:
            # Single graph - take mean over all nodes
            x = torch.mean(x, dim=0, keepdim=True)  # [1, hidden_dim]

        # Step 3: Project to capsule input format
        x = self.projection(x)  # [batch_size, num_input_capsules * input_capsule_dim]

        # Step 4: Reshape to primary capsules format
        batch_size = x.size(0)
        primary_capsules = x.view(
            batch_size,
            self.num_input_capsules,
            self.input_capsule_dim
        )  # [batch_size, num_input_capsules, input_capsule_dim]

        # Step 5: Pass through capsule layer
        output_capsules = self.capsule_layer(primary_capsules)
        # [batch_size, num_output_capsules, output_capsule_dim]

        # Step 6: Flatten to final graph features
        graph_features = output_capsules.view(batch_size, -1)
        # [batch_size, num_output_capsules * output_capsule_dim]

        return graph_features

    def get_output_dim(self) -> int:
        """
        Get the output dimension of the extractor.

        Returns
        -------
        int
            Output feature dimension
        """
        return self.output_dim


def test_capsule_layer():
    """
    Test function for the CapsuleLayer implementation.
    """
    print("🧪 Testing CapsuleLayer Implementation")
    print("=" * 40)
    
    # Test parameters
    batch_size = 4
    num_input_capsules = 8
    num_output_capsules = 10
    input_capsule_dim = 16
    output_capsule_dim = 8
    num_routing_iterations = 3
    
    # Create test input
    u = torch.randn(batch_size, num_input_capsules, input_capsule_dim)
    print(f"📊 Input shape: {u.shape}")
    
    # Create capsule layer
    capsule_layer = CapsuleLayer(
        num_input_capsules=num_input_capsules,
        num_output_capsules=num_output_capsules,
        input_capsule_dim=input_capsule_dim,
        output_capsule_dim=output_capsule_dim,
        num_routing_iterations=num_routing_iterations
    )
    
    # Forward pass
    print(f"\n🔄 Running forward pass...")
    v = capsule_layer(u)
    print(f"📊 Output shape: {v.shape}")
    
    # Verify output properties
    expected_shape = (batch_size, num_output_capsules, output_capsule_dim)
    assert v.shape == expected_shape, f"Expected {expected_shape}, got {v.shape}"
    
    # Check that capsule lengths are <= 1 (due to squashing)
    capsule_lengths = torch.sqrt(torch.sum(v ** 2, dim=-1))
    max_length = torch.max(capsule_lengths)
    print(f"📏 Maximum capsule length: {max_length:.4f} (should be ≤ 1.0)")
    
    print(f"✅ CapsuleLayer test passed!")
    
    return capsule_layer, u, v


def test_graph_capsule_extractor():
    """
    Test function for the GraphCapsuleExtractor implementation.
    """
    print("\n🧪 Testing GraphCapsuleExtractor Implementation")
    print("=" * 50)

    # Test parameters
    batch_size = 2
    num_nodes_per_graph = [10, 15]  # Different sized graphs
    input_dim = 3  # Node feature dimension
    hidden_dim = 32
    num_gcn_layers = 3

    # Create test graph data
    from torch_geometric.data import Data, Batch

    graphs = []
    for i, num_nodes in enumerate(num_nodes_per_graph):
        # Create random node features
        x = torch.randn(num_nodes, input_dim)

        # Create random edge index (ensure it's a valid graph)
        num_edges = min(num_nodes * 2, 20)  # Reasonable number of edges
        edge_index = torch.randint(0, num_nodes, (2, num_edges))

        # Create graph data
        graph = Data(x=x, edge_index=edge_index)
        graphs.append(graph)

    # Create batch
    batch_data = Batch.from_data_list(graphs)
    print(f"📊 Batch info:")
    print(f"   • Batch size: {len(graphs)}")
    print(f"   • Total nodes: {batch_data.x.shape[0]}")
    print(f"   • Total edges: {batch_data.edge_index.shape[1]}")
    print(f"   • Node features: {batch_data.x.shape}")

    # Create GraphCapsuleExtractor
    extractor = GraphCapsuleExtractor(
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        num_gcn_layers=num_gcn_layers,
        num_input_capsules=8,
        num_output_capsules=5,
        input_capsule_dim=4,
        output_capsule_dim=8,
        num_routing_iterations=3
    )

    # Forward pass
    print(f"\n🔄 Running forward pass...")
    graph_features = extractor(batch_data)
    print(f"📊 Output shape: {graph_features.shape}")

    # Verify output properties
    expected_batch_size = len(graphs)
    expected_feature_dim = extractor.get_output_dim()
    expected_shape = (expected_batch_size, expected_feature_dim)

    assert graph_features.shape == expected_shape, f"Expected {expected_shape}, got {graph_features.shape}"

    # Test with single graph
    print(f"\n🔄 Testing single graph...")
    single_graph = graphs[0]
    single_features = extractor(single_graph)
    print(f"📊 Single graph output shape: {single_features.shape}")

    expected_single_shape = (1, expected_feature_dim)
    assert single_features.shape == expected_single_shape, f"Expected {expected_single_shape}, got {single_features.shape}"

    print(f"✅ GraphCapsuleExtractor test passed!")
    print(f"   • Batch processing: ✅")
    print(f"   • Single graph processing: ✅")
    print(f"   • Output dimension: {expected_feature_dim}")

    return extractor, batch_data, graph_features


if __name__ == "__main__":
    # Run tests when script is executed directly
    test_capsule_layer()
    test_graph_capsule_extractor()
