#!/usr/bin/env python3
"""
Graph Federated Learning Agent Models

This module contains neural network models for grid agent training,
including capsule networks and graph neural network architectures
for power grid topology optimization.

Author: Grid Agent Project
Date: 2025-07-13
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional


class CapsuleLayer(nn.Module):
    """
    Capsule Layer with Dynamic Routing Algorithm.
    
    This layer implements the capsule network architecture with dynamic routing
    between capsules. Capsules are groups of neurons that represent the instantiation
    parameters of specific entities, providing better representation of hierarchical
    relationships in data.
    
    The dynamic routing algorithm iteratively refines the routing weights between
    input and output capsules based on the agreement between prediction vectors.
    """
    
    def __init__(
        self,
        num_input_capsules: int,
        num_output_capsules: int,
        input_capsule_dim: int,
        output_capsule_dim: int,
        num_routing_iterations: int = 3
    ):
        """
        Initialize the Capsule Layer.
        
        Parameters
        ----------
        num_input_capsules : int
            Number of input capsules
        num_output_capsules : int
            Number of output capsules
        input_capsule_dim : int
            Dimension of each input capsule
        output_capsule_dim : int
            Dimension of each output capsule
        num_routing_iterations : int, default=3
            Number of iterations for the dynamic routing algorithm
        """
        super(CapsuleLayer, self).__init__()
        
        self.num_input_capsules = num_input_capsules
        self.num_output_capsules = num_output_capsules
        self.input_capsule_dim = input_capsule_dim
        self.output_capsule_dim = output_capsule_dim
        self.num_routing_iterations = num_routing_iterations
        
        # Weight matrix for affine transformations
        # Shape: [1, num_input_capsules, num_output_capsules, output_capsule_dim, input_capsule_dim]
        self.W = nn.Parameter(
            torch.randn(
                1,
                num_input_capsules,
                num_output_capsules,
                output_capsule_dim,
                input_capsule_dim
            ) * 0.01  # Small initialization for stability
        )
        
        print(f"🔗 CapsuleLayer initialized:")
        print(f"   • Input capsules: {num_input_capsules} × {input_capsule_dim}D")
        print(f"   • Output capsules: {num_output_capsules} × {output_capsule_dim}D")
        print(f"   • Routing iterations: {num_routing_iterations}")
        print(f"   • Weight matrix shape: {self.W.shape}")
    
    def forward(self, u: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the capsule layer with dynamic routing.
        
        Parameters
        ----------
        u : torch.Tensor
            Input tensor of shape [batch_size, num_input_capsules, input_capsule_dim]
            
        Returns
        -------
        torch.Tensor
            Output capsules of shape [batch_size, num_output_capsules, output_capsule_dim]
        """
        
        batch_size = u.size(0)
        
        # Step 1: Expand input and weight matrix for batch matrix multiplication
        # u: [batch_size, num_input_capsules, input_capsule_dim]
        # -> [batch_size, num_input_capsules, 1, 1, input_capsule_dim]
        u_expanded = u.unsqueeze(2).unsqueeze(3)
        
        # W: [1, num_input_capsules, num_output_capsules, output_capsule_dim, input_capsule_dim]
        # -> [batch_size, num_input_capsules, num_output_capsules, output_capsule_dim, input_capsule_dim]
        W_expanded = self.W.expand(batch_size, -1, -1, -1, -1)
        
        # Compute prediction vectors u_hat
        # u_hat: [batch_size, num_input_capsules, num_output_capsules, output_capsule_dim]
        u_hat = torch.matmul(W_expanded, u_expanded.unsqueeze(-1)).squeeze(-1)
        
        # Step 2: Initialize routing logits
        # b: [batch_size, num_input_capsules, num_output_capsules, 1]
        b = torch.zeros(
            batch_size,
            self.num_input_capsules,
            self.num_output_capsules,
            1,
            device=u.device,
            dtype=u.dtype
        )
        
        # Step 3: Dynamic routing algorithm
        for iteration in range(self.num_routing_iterations):
            
            # Step 3i: Calculate routing weights using softmax
            # c: [batch_size, num_input_capsules, num_output_capsules, 1]
            c = F.softmax(b, dim=2)  # Softmax over num_output_capsules dimension
            
            # Step 3ii: Compute weighted sum of prediction vectors
            # s: [batch_size, num_output_capsules, output_capsule_dim]
            s = torch.sum(c * u_hat, dim=1)  # Sum over num_input_capsules dimension
            
            # Step 3iii: Apply squashing non-linearity
            # v: [batch_size, num_output_capsules, output_capsule_dim]
            v = self.squash(s)
            
            # Step 3iv: Update routing logits (except for last iteration)
            if iteration < self.num_routing_iterations - 1:
                # Expand v for agreement calculation
                # v_expanded: [batch_size, 1, num_output_capsules, output_capsule_dim]
                v_expanded = v.unsqueeze(1)
                
                # Calculate agreement between u_hat and v
                # agreement: [batch_size, num_input_capsules, num_output_capsules, 1]
                agreement = torch.sum(u_hat * v_expanded, dim=-1, keepdim=True)
                
                # Update routing logits
                b = b + agreement
        
        return v
    
    def squash(self, s: torch.Tensor) -> torch.Tensor:
        """
        Apply the squashing non-linearity to ensure capsule outputs have unit length.
        
        The squashing function is defined as:
        v = (s_norm_sq / (1 + s_norm_sq)) * (s / s_norm)
        
        where s_norm is the L2 norm of s and s_norm_sq is its square.
        
        Parameters
        ----------
        s : torch.Tensor
            Input tensor to be squashed
            
        Returns
        -------
        torch.Tensor
            Squashed tensor with the same shape as input
        """
        
        # Calculate L2 norm squared
        # s_norm_sq: [batch_size, num_output_capsules, 1]
        s_norm_sq = torch.sum(s ** 2, dim=-1, keepdim=True)
        
        # Calculate L2 norm (add small epsilon for numerical stability)
        # s_norm: [batch_size, num_output_capsules, 1]
        s_norm = torch.sqrt(s_norm_sq + 1e-8)
        
        # Apply squashing function
        # Scale factor: s_norm_sq / (1 + s_norm_sq)
        scale_factor = s_norm_sq / (1.0 + s_norm_sq)
        
        # Unit vector: s / s_norm
        unit_vector = s / s_norm
        
        # Final squashed output
        v = scale_factor * unit_vector
        
        return v
    
    def extra_repr(self) -> str:
        """
        Extra representation for debugging and model summary.
        
        Returns
        -------
        str
            String representation of the layer parameters
        """
        return (
            f"num_input_capsules={self.num_input_capsules}, "
            f"num_output_capsules={self.num_output_capsules}, "
            f"input_capsule_dim={self.input_capsule_dim}, "
            f"output_capsule_dim={self.output_capsule_dim}, "
            f"num_routing_iterations={self.num_routing_iterations}"
        )


class PrimaryCapsuleLayer(nn.Module):
    """
    Primary Capsule Layer for converting convolutional features to capsules.
    
    This layer typically follows convolutional layers and creates the first
    level of capsules from feature maps.
    """
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        capsule_dim: int,
        kernel_size: int = 9,
        stride: int = 2
    ):
        """
        Initialize the Primary Capsule Layer.
        
        Parameters
        ----------
        in_channels : int
            Number of input channels
        out_channels : int
            Number of output channels (will be reshaped to capsules)
        capsule_dim : int
            Dimension of each capsule
        kernel_size : int, default=9
            Convolution kernel size
        stride : int, default=2
            Convolution stride
        """
        super(PrimaryCapsuleLayer, self).__init__()
        
        self.capsule_dim = capsule_dim
        self.num_capsules = out_channels // capsule_dim
        
        # Convolutional layer to generate capsule inputs
        self.conv = nn.Conv2d(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=kernel_size,
            stride=stride
        )
        
        print(f"🔗 PrimaryCapsuleLayer initialized:")
        print(f"   • Input channels: {in_channels}")
        print(f"   • Output capsules: {self.num_capsules} × {capsule_dim}D")
        print(f"   • Kernel size: {kernel_size}, Stride: {stride}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the primary capsule layer.
        
        Parameters
        ----------
        x : torch.Tensor
            Input tensor from convolutional layers
            
        Returns
        -------
        torch.Tensor
            Output capsules
        """
        
        # Apply convolution
        conv_out = self.conv(x)
        
        # Reshape to capsules and apply squashing
        batch_size = conv_out.size(0)
        
        # Reshape: [batch_size, out_channels, height, width] 
        # -> [batch_size, num_capsules, capsule_dim, height, width]
        capsules = conv_out.view(
            batch_size,
            self.num_capsules,
            self.capsule_dim,
            conv_out.size(2),
            conv_out.size(3)
        )
        
        # Flatten spatial dimensions and transpose
        # -> [batch_size, num_capsules * height * width, capsule_dim]
        capsules = capsules.view(batch_size, self.num_capsules, -1).transpose(1, 2)
        capsules = capsules.contiguous().view(batch_size, -1, self.capsule_dim)
        
        # Apply squashing non-linearity
        return self.squash(capsules)
    
    def squash(self, s: torch.Tensor) -> torch.Tensor:
        """Apply squashing non-linearity (same as CapsuleLayer)."""
        s_norm_sq = torch.sum(s ** 2, dim=-1, keepdim=True)
        s_norm = torch.sqrt(s_norm_sq + 1e-8)
        scale_factor = s_norm_sq / (1.0 + s_norm_sq)
        unit_vector = s / s_norm
        return scale_factor * unit_vector


def test_capsule_layer():
    """
    Test function for the CapsuleLayer implementation.
    """
    print("🧪 Testing CapsuleLayer Implementation")
    print("=" * 40)
    
    # Test parameters
    batch_size = 4
    num_input_capsules = 8
    num_output_capsules = 10
    input_capsule_dim = 16
    output_capsule_dim = 8
    num_routing_iterations = 3
    
    # Create test input
    u = torch.randn(batch_size, num_input_capsules, input_capsule_dim)
    print(f"📊 Input shape: {u.shape}")
    
    # Create capsule layer
    capsule_layer = CapsuleLayer(
        num_input_capsules=num_input_capsules,
        num_output_capsules=num_output_capsules,
        input_capsule_dim=input_capsule_dim,
        output_capsule_dim=output_capsule_dim,
        num_routing_iterations=num_routing_iterations
    )
    
    # Forward pass
    print(f"\n🔄 Running forward pass...")
    v = capsule_layer(u)
    print(f"📊 Output shape: {v.shape}")
    
    # Verify output properties
    expected_shape = (batch_size, num_output_capsules, output_capsule_dim)
    assert v.shape == expected_shape, f"Expected {expected_shape}, got {v.shape}"
    
    # Check that capsule lengths are <= 1 (due to squashing)
    capsule_lengths = torch.sqrt(torch.sum(v ** 2, dim=-1))
    max_length = torch.max(capsule_lengths)
    print(f"📏 Maximum capsule length: {max_length:.4f} (should be ≤ 1.0)")
    
    print(f"✅ CapsuleLayer test passed!")
    
    return capsule_layer, u, v


if __name__ == "__main__":
    # Run tests when script is executed directly
    test_capsule_layer()
