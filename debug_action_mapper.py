#!/usr/bin/env python3
"""
Debug script for ActionSpaceMapper
"""

import grid2op
from lightsim2grid import LightSimBackend
from converters import ActionSpaceMapper

# Create environment and mapper
env = grid2op.make("l2rpn_case14_sandbox", backend=LightSimBackend())
mapper = ActionSpaceMapper(env)

# Get action space info
info = mapper.get_action_space_info()

print("Debug info:")
print(f"Info keys: {list(info.keys())}")
print(f"Info contents: {info}")

env.close()
