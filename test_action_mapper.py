#!/usr/bin/env python3
"""
Test Script for ActionSpaceMapper

This script tests the ActionSpaceMapper to ensure it correctly creates
a discrete action space and maps indices to valid Grid2Op actions.

Author: Grid Agent Project
Date: 2025-07-13
"""

import grid2op
from lightsim2grid import LightSimBackend
from converters import ActionSpaceMapper


def test_action_mapper():
    """Test the ActionSpaceMapper functionality."""
    print("🧪 Testing ActionSpaceMapper")
    print("=" * 40)
    
    try:
        # Create environment
        env = grid2op.make("l2rpn_case14_sandbox", backend=LightSimBackend())
        print("✅ Environment created successfully")
        
        # Initialize action mapper
        mapper = ActionSpaceMapper(env)
        print("✅ ActionSpaceMapper initialized successfully")
        
        # Test basic properties
        print(f"\n📊 Action Space Information:")
        print(f"   • Total actions: {mapper.action_space_size}")
        
        # Get action space info
        info = mapper.get_action_space_info()
        print(f"   • Do-nothing actions: {info['do_nothing_actions']}")
        print(f"   • Topology actions: {info['topology_actions']}")
        print(f"   • Substations with actions: {info['substations_with_actions']}")
        
        # Test do-nothing action
        print(f"\n🔧 Testing Actions:")
        action_0 = mapper.map_to_grid2op_action(0)
        print(f"   • Action 0: {mapper.get_action_description(0)}")
        print(f"     Type: {type(action_0).__name__}")
        
        # Test a few topology actions
        for i in range(1, min(6, mapper.action_space_size)):
            action = mapper.map_to_grid2op_action(i)
            description = mapper.get_action_description(i)
            print(f"   • Action {i}: {description}")
        
        # Test action validation in environment
        print(f"\n🔍 Testing Action Validation:")
        obs = env.reset()
        
        # Test do-nothing action
        action_0 = mapper.map_to_grid2op_action(0)
        obs_new, reward, done, info = env.step(action_0)
        print(f"   • Do-nothing action: Valid ✅ (reward: {reward:.3f})")
        
        # Test a topology action
        if mapper.action_space_size > 1:
            obs = env.reset()  # Reset for clean test
            action_1 = mapper.map_to_grid2op_action(1)
            obs_new, reward, done, info = env.step(action_1)
            status = "Valid ✅" if not done else "Invalid ❌"
            print(f"   • Topology action 1: {status} (reward: {reward:.3f})")
        
        # Test edge cases
        print(f"\n🧪 Testing Edge Cases:")
        
        # Test invalid action index
        try:
            invalid_action = mapper.map_to_grid2op_action(mapper.action_space_size)
            print("   • Invalid index handling: Failed ❌")
        except ValueError:
            print("   • Invalid index handling: Passed ✅")
        
        # Test negative index
        try:
            invalid_action = mapper.map_to_grid2op_action(-1)
            print("   • Negative index handling: Failed ❌")
        except ValueError:
            print("   • Negative index handling: Passed ✅")
        
        print(f"\n🎯 Summary:")
        print(f"   • Action space size: {mapper.action_space_size}")
        print(f"   • All basic tests passed ✅")
        
        # Show detailed action space breakdown
        print(f"\n📋 Detailed Action Space Breakdown:")
        if 'actions_per_substation' in info:
            for sub_id, count in info['actions_per_substation'].items():
                print(f"   • Substation {sub_id}: {count} actions")
        else:
            print(f"   • Available keys in info: {list(info.keys())}")
            print(f"   • Error: 'actions_per_substation' key not found")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_action_consistency():
    """Test that actions are consistent and deterministic."""
    print("\n🔄 Testing Action Consistency")
    print("-" * 30)
    
    try:
        env = grid2op.make("l2rpn_case14_sandbox", backend=LightSimBackend())
        mapper = ActionSpaceMapper(env)
        
        # Test that same index always produces same action
        action1_first = mapper.map_to_grid2op_action(1)
        action1_second = mapper.map_to_grid2op_action(1)
        
        # Compare action representations
        if str(action1_first) == str(action1_second):
            print("   • Action consistency: Passed ✅")
        else:
            print("   • Action consistency: Failed ❌")
        
        # Test multiple random actions
        import random
        test_indices = random.sample(range(mapper.action_space_size), 
                                   min(5, mapper.action_space_size))
        
        all_valid = True
        for idx in test_indices:
            try:
                action = mapper.map_to_grid2op_action(idx)
                description = mapper.get_action_description(idx)
                print(f"   • Action {idx}: Valid ✅")
            except Exception as e:
                print(f"   • Action {idx}: Failed ❌ - {e}")
                all_valid = False
        
        if all_valid:
            print("   • All random actions: Valid ✅")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ Consistency test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 ActionSpaceMapper Test Suite")
    print("=" * 50)
    
    success1 = test_action_mapper()
    success2 = test_action_consistency()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All tests passed! ActionSpaceMapper is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    main()
