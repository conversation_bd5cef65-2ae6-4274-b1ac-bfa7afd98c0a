#!/usr/bin/env python3
"""
Test Script for GraphObservationConverter

This script comprehensively tests the GraphObservationConverter to ensure
it correctly converts Grid2Op observations to PyTorch Geometric graph data.

Author: Grid Agent Project
Date: 2025-07-13
"""

import torch
import numpy as np
import grid2op
from lightsim2grid import LightSimBackend
from converters import GraphObservationConverter
import matplotlib.pyplot as plt
import networkx as nx
from torch_geometric.data import Data


def test_basic_functionality():
    """Test basic converter initialization and conversion."""
    print("🧪 Test 1: Basic Functionality")
    print("-" * 30)
    
    try:
        # Create environment
        env = grid2op.make("l2rpn_case14_sandbox", backend=LightSimBackend())
        print("✅ Environment created successfully")
        
        # Initialize converter
        converter = GraphObservationConverter(env, num_episodes=1, device='cpu')
        print("✅ Converter initialized successfully")
        
        # Get initial observation
        obs = env.reset()
        print("✅ Environment reset successfully")
        
        # Convert observation
        graph_data = converter.convert(obs)
        print("✅ Observation converted successfully")
        
        # Check if it's a valid Data object
        assert isinstance(graph_data, Data), "Output should be torch_geometric.data.Data"
        print("✅ Output is valid PyTorch Geometric Data object")
        
        return True, env, converter
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False, None, None


def test_tensor_properties(converter, env):
    """Test tensor shapes, types, and device placement."""
    print("\n🧪 Test 2: Tensor Properties")
    print("-" * 30)
    
    try:
        obs = env.reset()
        graph_data = converter.convert(obs)
        
        # Test tensor types
        assert graph_data.x.dtype == torch.float32, f"Node features should be float32, got {graph_data.x.dtype}"
        assert graph_data.edge_index.dtype == torch.long, f"Edge index should be long, got {graph_data.edge_index.dtype}"
        assert graph_data.edge_attr.dtype == torch.float32, f"Edge features should be float32, got {graph_data.edge_attr.dtype}"
        print("✅ All tensors have correct data types")
        
        # Test tensor shapes
        num_nodes = graph_data.x.shape[0]
        num_edges = graph_data.edge_index.shape[1]
        
        assert graph_data.x.shape[1] == 3, f"Node features should have 3 dimensions, got {graph_data.x.shape[1]}"
        assert graph_data.edge_index.shape[0] == 2, f"Edge index should have shape [2, num_edges], got {graph_data.edge_index.shape}"
        assert graph_data.edge_attr.shape == (num_edges, 4), f"Edge features should have shape [num_edges, 4], got {graph_data.edge_attr.shape}"
        print("✅ All tensors have correct shapes")
        
        # Test device placement
        expected_device = converter.device
        assert str(graph_data.x.device).startswith(expected_device) or expected_device == 'cpu', f"Tensors should be on {expected_device}"
        print(f"✅ Tensors are on correct device: {graph_data.x.device}")
        
        # Print tensor information
        print(f"📊 Graph Statistics:")
        print(f"   • Number of nodes: {num_nodes}")
        print(f"   • Number of edges: {num_edges}")
        print(f"   • Node features shape: {graph_data.x.shape}")
        print(f"   • Edge index shape: {graph_data.edge_index.shape}")
        print(f"   • Edge features shape: {graph_data.edge_attr.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tensor properties test failed: {e}")
        return False


def test_normalization(converter, env):
    """Test that normalization is working correctly."""
    print("\n🧪 Test 3: Feature Normalization")
    print("-" * 30)
    
    try:
        # Collect multiple observations
        observations = []
        for _ in range(5):
            obs = env.reset()
            observations.append(obs)
            
            # Take a few steps
            for _ in range(3):
                action = env.action_space()  # Do nothing action
                obs, _, done, _ = env.step(action)
                if not done:
                    observations.append(obs)
        
        # Convert all observations
        graph_data_list = []
        for obs in observations:
            try:
                graph_data = converter.convert(obs)
                if graph_data.x.shape[0] > 0:  # Only add non-empty graphs
                    graph_data_list.append(graph_data)
            except:
                continue
        
        if len(graph_data_list) > 1:
            # Check normalization statistics
            all_node_features = torch.cat([data.x for data in graph_data_list], dim=0)
            all_edge_features = torch.cat([data.edge_attr for data in graph_data_list], dim=0)
            
            # Node features should be roughly normalized (mean ~0, std ~1)
            node_means = torch.mean(all_node_features, dim=0)
            node_stds = torch.std(all_node_features, dim=0)
            
            print(f"📈 Node Feature Statistics:")
            print(f"   • Means: {node_means.cpu().numpy()}")
            print(f"   • Stds: {node_stds.cpu().numpy()}")
            
            # Edge features (excluding binary cooldown feature)
            edge_means = torch.mean(all_edge_features[:, :3], dim=0)  # Exclude cooldown
            edge_stds = torch.std(all_edge_features[:, :3], dim=0)
            
            print(f"📈 Edge Feature Statistics (excluding cooldown):")
            print(f"   • Means: {edge_means.cpu().numpy()}")
            print(f"   • Stds: {edge_stds.cpu().numpy()}")
            
            # Check if normalization is reasonable (not too extreme)
            assert torch.all(torch.abs(node_means) < 2.0), "Node feature means seem too large"
            assert torch.all(node_stds > 0.1), "Node feature stds seem too small"
            print("✅ Normalization appears to be working correctly")
        else:
            print("⚠️  Not enough valid observations to test normalization")
        
        return True
        
    except Exception as e:
        print(f"❌ Normalization test failed: {e}")
        return False


def test_edge_cases(converter, env):
    """Test edge cases and error handling."""
    print("\n🧪 Test 4: Edge Cases")
    print("-" * 30)
    
    try:
        # Test with normal observation
        obs = env.reset()
        graph_data = converter.convert(obs)
        print("✅ Normal observation conversion works")
        
        # Test multiple conversions (should be consistent)
        graph_data2 = converter.convert(obs)
        assert torch.allclose(graph_data.x, graph_data2.x), "Multiple conversions should be identical"
        assert torch.equal(graph_data.edge_index, graph_data2.edge_index), "Edge indices should be identical"
        print("✅ Multiple conversions are consistent")
        
        # Test with different observations
        action = env.action_space()
        obs2, _, done, _ = env.step(action)
        if not done:
            graph_data3 = converter.convert(obs2)
            print("✅ Different observation conversion works")
        
        return True
        
    except Exception as e:
        print(f"❌ Edge cases test failed: {e}")
        return False


def test_graph_structure_consistency(converter, env):
    """Test that the graph structure makes sense."""
    print("\n🧪 Test 5: Graph Structure Consistency")
    print("-" * 30)
    
    try:
        obs = env.reset()
        
        # Get original networkx graph
        nx_graph = obs.get_energy_graph()
        
        # Convert to our format
        graph_data = converter.convert(obs)
        
        # Check node count consistency
        assert graph_data.x.shape[0] == nx_graph.number_of_nodes(), "Node count mismatch"
        print(f"✅ Node count consistent: {graph_data.x.shape[0]} nodes")
        
        # Check edge count consistency
        assert graph_data.edge_index.shape[1] == nx_graph.number_of_edges(), "Edge count mismatch"
        print(f"✅ Edge count consistent: {graph_data.edge_index.shape[1]} edges")
        
        # Check edge index validity
        max_node_id = graph_data.x.shape[0] - 1
        assert torch.all(graph_data.edge_index >= 0), "Edge indices should be non-negative"
        assert torch.all(graph_data.edge_index <= max_node_id), "Edge indices should be within node range"
        print("✅ Edge indices are valid")
        
        # Check for self-loops (should not exist in power grids)
        edge_index = graph_data.edge_index
        self_loops = (edge_index[0] == edge_index[1]).sum()
        print(f"📊 Self-loops detected: {self_loops}")
        
        return True
        
    except Exception as e:
        print(f"❌ Graph structure test failed: {e}")
        return False


def test_feature_ranges(converter, env):
    """Test that feature values are in reasonable ranges."""
    print("\n🧪 Test 6: Feature Value Ranges")
    print("-" * 30)
    
    try:
        obs = env.reset()
        graph_data = converter.convert(obs)
        
        # Check for NaN or infinite values
        assert not torch.any(torch.isnan(graph_data.x)), "Node features contain NaN"
        assert not torch.any(torch.isinf(graph_data.x)), "Node features contain Inf"
        assert not torch.any(torch.isnan(graph_data.edge_attr)), "Edge features contain NaN"
        assert not torch.any(torch.isinf(graph_data.edge_attr)), "Edge features contain Inf"
        print("✅ No NaN or Inf values detected")
        
        # Check cooldown feature is binary
        cooldown_values = graph_data.edge_attr[:, 3]  # Last feature is cooldown
        unique_cooldown = torch.unique(cooldown_values)
        assert len(unique_cooldown) <= 2, "Cooldown should be binary"
        assert torch.all((cooldown_values == 0) | (cooldown_values == 1)), "Cooldown should be 0 or 1"
        print("✅ Cooldown feature is properly binary")
        
        # Print feature ranges
        print(f"📊 Feature Ranges:")
        print(f"   • Node features min: {graph_data.x.min(dim=0)[0].cpu().numpy()}")
        print(f"   • Node features max: {graph_data.x.max(dim=0)[0].cpu().numpy()}")
        print(f"   • Edge features min: {graph_data.edge_attr.min(dim=0)[0].cpu().numpy()}")
        print(f"   • Edge features max: {graph_data.edge_attr.max(dim=0)[0].cpu().numpy()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Feature ranges test failed: {e}")
        return False


def run_all_tests():
    """Run all tests and provide a summary."""
    print("🚀 GraphObservationConverter Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Test 1: Basic functionality
    success, env, converter = test_basic_functionality()
    test_results.append(("Basic Functionality", success))
    
    if not success:
        print("\n❌ Basic functionality failed. Stopping tests.")
        return
    
    # Test 2: Tensor properties
    success = test_tensor_properties(converter, env)
    test_results.append(("Tensor Properties", success))
    
    # Test 3: Normalization
    success = test_normalization(converter, env)
    test_results.append(("Feature Normalization", success))
    
    # Test 4: Edge cases
    success = test_edge_cases(converter, env)
    test_results.append(("Edge Cases", success))
    
    # Test 5: Graph structure
    success = test_graph_structure_consistency(converter, env)
    test_results.append(("Graph Structure", success))
    
    # Test 6: Feature ranges
    success = test_feature_ranges(converter, env)
    test_results.append(("Feature Ranges", success))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print("-" * 20)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! GraphObservationConverter is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    # Cleanup
    env.close()


if __name__ == "__main__":
    run_all_tests()
