#!/usr/bin/env python3
"""
Environment Fix and Alternative Setup

This script provides alternative environment setups and fixes
for Grid2Op stability issues.

Author: Grid Agent Project
Date: 2025-07-13
"""

import grid2op
from lightsim2grid import LightSimBackend
import numpy as np


def create_stable_environment():
    """
    Create a stable Grid2Op environment with proper configuration.
    
    Returns
    -------
    grid2op.Environment
        Configured stable environment
    """
    
    print("🔧 Creating stable Grid2Op environment...")
    
    # Try different environments in order of preference
    env_candidates = [
        "rte_case5_example",  # Smallest, most stable
        "rte_case14_realistic",  # Realistic but stable
        "educ_case14_storage",  # Educational, should be stable
        "l2rpn_case14_sandbox"  # Fallback
    ]
    
    for env_name in env_candidates:
        try:
            print(f"   Trying {env_name}...")
            
            # Create environment with minimal configuration
            env = grid2op.make(
                env_name,
                backend=LightSimBackend(),
                action_class=None,  # Use default
                observation_class=None,  # Use default
                reward_class=None,  # Use default reward first
                chronics_class=None,  # Use default
                data_feeding_kwargs={},
                chronics_path=None,
                grid_path=None,
                volagecontroler_class=None,
                thermal_limits=None,
                with_forecast=False,  # Disable forecasting for stability
                epsilon_poly=1e-4,
                tol_poly=1e-8
            )
            
            # Test basic functionality
            obs = env.reset()
            action = env.action_space()  # Do nothing
            obs, reward, done, info = env.step(action)
            
            if not done and info.get('exception', None) is None:
                print(f"   ✅ {env_name} is stable!")
                return env
            else:
                print(f"   ❌ {env_name} failed basic test")
                env.close()
                
        except Exception as e:
            print(f"   ❌ {env_name} failed to create: {e}")
            continue
    
    raise RuntimeError("No stable environment found!")


def test_environment_with_default_reward(env, num_steps=50):
    """
    Test environment with default reward function.
    
    Parameters
    ----------
    env : grid2op.Environment
        Environment to test
    num_steps : int
        Number of steps to test
        
    Returns
    -------
    dict
        Test results
    """
    
    print(f"🧪 Testing environment with default reward...")
    
    obs = env.reset()
    total_reward = 0
    step_count = 0
    
    print(f"   Initial state: max_rho = {obs.rho.max():.3f}")
    
    for step in range(num_steps):
        # Do nothing action
        action = env.action_space()
        obs, reward, done, info = env.step(action)
        
        total_reward += reward
        step_count += 1
        
        if step % 10 == 0:
            print(f"   Step {step}: reward = {reward:.3f}, max_rho = {obs.rho.max():.3f}, done = {done}")
        
        if done:
            if info.get('exception', None) is not None:
                print(f"   ❌ Episode ended with error: {info['exception']}")
                break
            else:
                print(f"   ✅ Episode ended naturally at step {step}")
                break
    
    return {
        'total_reward': total_reward,
        'avg_reward': total_reward / step_count if step_count > 0 else 0,
        'steps_completed': step_count,
        'success': step_count > 10  # At least 10 steps without error
    }


class SimpleReward:
    """
    Very simple reward function for testing.
    """
    
    def __init__(self):
        self.reward_min = -10.0
        self.reward_max = 10.0
    
    def initialize(self, env):
        pass
    
    def __call__(self, action, env, has_error, is_done, is_illegal, is_ambiguous):
        """Simple reward: +1 for survival, -10 for errors."""
        
        if has_error or is_illegal:
            return -10.0
        
        # Get current observation
        observation = env.current_obs
        max_rho = observation.rho.max()
        
        # Simple reward based on line loading
        if max_rho < 0.8:
            return 2.0  # Good operation
        elif max_rho < 0.9:
            return 1.0  # Acceptable operation
        elif max_rho < 0.95:
            return 0.5  # Risky but okay
        else:
            return -1.0  # Very risky


def main():
    """Main function to test and fix environment issues."""
    
    print("🔧 Grid2Op Environment Fix and Test")
    print("=" * 50)
    
    # Step 1: Create stable environment
    try:
        env = create_stable_environment()
        print(f"✅ Stable environment created: {env.name}")
        print(f"   • Lines: {env.n_line}, Substations: {env.n_sub}")
        print(f"   • Generators: {env.n_gen}, Loads: {env.n_load}")
    except Exception as e:
        print(f"❌ Failed to create stable environment: {e}")
        return
    
    # Step 2: Test with default reward
    print(f"\n📊 Testing with default reward function...")
    default_results = test_environment_with_default_reward(env, num_steps=50)
    
    print(f"📈 Default Reward Results:")
    print(f"   • Steps completed: {default_results['steps_completed']}")
    print(f"   • Average reward: {default_results['avg_reward']:.3f}")
    print(f"   • Success: {default_results['success']}")
    
    # Step 3: Test with simple custom reward
    print(f"\n📊 Testing with simple custom reward...")
    
    # Close current environment
    env.close()
    
    # Create new environment with simple reward
    try:
        env = grid2op.make(
            env.name,
            backend=LightSimBackend(),
            reward_class=SimpleReward,
            with_forecast=False
        )
        
        simple_results = test_environment_with_default_reward(env, num_steps=50)
        
        print(f"📈 Simple Reward Results:")
        print(f"   • Steps completed: {simple_results['steps_completed']}")
        print(f"   • Average reward: {simple_results['avg_reward']:.3f}")
        print(f"   • Success: {simple_results['success']}")
        
    except Exception as e:
        print(f"❌ Simple reward test failed: {e}")
    
    # Step 4: Recommendations
    print(f"\n🎯 Recommendations:")
    
    if default_results['success'] or simple_results.get('success', False):
        print(f"✅ Environment is working!")
        print(f"   • Use environment: {env.name}")
        print(f"   • Use SimpleReward class for stable training")
        print(f"   • Update main.py with these settings")
        
        # Generate fixed main.py snippet
        print(f"\n📝 Update main.py with:")
        print(f"```python")
        print(f"# Replace environment creation with:")
        print(f"env = grid2op.make(")
        print(f"    '{env.name}',")
        print(f"    backend=LightSimBackend(),")
        print(f"    reward_class=SimpleReward,  # Use simple reward")
        print(f"    with_forecast=False  # Disable forecasting")
        print(f")")
        print(f"```")
        
    else:
        print(f"❌ Environment issues persist")
        print(f"   • Try different Grid2Op version")
        print(f"   • Use PandaPowerBackend instead of LightSimBackend")
        print(f"   • Create custom chronics data")
    
    env.close()
    print(f"\n🎉 Environment testing completed!")


if __name__ == "__main__":
    main()
