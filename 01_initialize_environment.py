#!/usr/bin/env python3
"""
Grid2Op Environment Initialization Script

This script demonstrates how to initialize a Grid2Op environment with the LightSim backend
for power grid simulation and reinforcement learning applications.

Author: Grid Agent Project
Date: 2025-07-13
"""

import grid2op
from lightsim2grid import LightSimBackend
import numpy as np

def main():
    """
    Initialize and inspect a Grid2Op environment with LightSim backend.
    """
    
    print("🔌 Grid2Op Environment Initialization")
    print("=" * 50)
    
    # Step 1: Define environment parameters
    env_params = {
        "env_name": "l2rpn_case14_sandbox",
        "backend": LightSimBackend(),
        "action_class": None,  # Use default action class
        "observation_class": None,  # Use default observation class
        "reward_class": None,  # Use default reward class
        "other_rewards": {},
        "chronics_class": None,  # Use default chronics class
        "data_feeding_kwargs": {},
        "chronics_path": None,  # Use default path
        "grid_path": None,  # Use default grid
        "volagecontroler_class": None,  # Use default voltage controller
        "thermal_limits": None,  # Use default thermal limits
        "with_forecast": True,  # Enable forecasting
        "epsilon_poly": 1e-4,  # Numerical precision
        "tol_poly": 1e-8,  # Tolerance for polynomial solver
        "other_class_pp_kwargs": {},
        "_add_to_name": "",
        "_compat_glop_version": None,
        "_read_from_local_dir": None
    }
    
    print(f"📋 Environment Parameters:")
    print(f"   • Environment Name: {env_params['env_name']}")
    print(f"   • Backend: {type(env_params['backend']).__name__}")
    print(f"   • Forecasting Enabled: {env_params['with_forecast']}")
    print()
    
    # Step 2: Create the Grid2Op environment
    print("🏗️  Creating Grid2Op environment...")
    try:
        env = grid2op.make(
            env_params["env_name"],
            backend=env_params["backend"]
        )
        print("✅ Environment created successfully!")
        print(f"   Environment type: {type(env).__name__}")
        print()
        
    except Exception as e:
        print(f"❌ Error creating environment: {e}")
        return
    
    # Step 3: Inspect observation space
    print("🔍 Observation Space Analysis:")
    print("-" * 30)
    
    # Get observation space attributes
    obs_attr_list = env.observation_space.attr_list_vect
    obs_shape = env.observation_space.shape
    
    print(f"📊 Observation Space Attributes ({len(obs_attr_list)} total):")
    for i, attr in enumerate(obs_attr_list, 1):
        print(f"   {i:2d}. {attr}")
    
    print(f"\n📏 Observation Vector Dimensions: {obs_shape}")
    if obs_shape is not None and len(obs_shape) > 0:
        total_obs_size = sum(obs_shape) if hasattr(obs_shape, '__iter__') else obs_shape
        print(f"   Total observation vector size: {total_obs_size}")
        print(f"   Number of observation components: {len(obs_shape)}")
    else:
        print(f"   Total observation vector size: N/A")
    print()
    
    # Step 4: Inspect action space
    print("🎮 Action Space Analysis:")
    print("-" * 25)
    
    action_class_name = env.action_space.__class__.__name__
    action_shape = env.action_space.shape
    
    print(f"🏷️  Action Space Class: {action_class_name}")
    print(f"📏 Action Space Dimensions: {action_shape}")
    if action_shape is not None and len(action_shape) > 0:
        total_action_size = sum(action_shape) if hasattr(action_shape, '__iter__') and len(action_shape) > 1 else action_shape[0]
        print(f"   Total action vector size: {total_action_size}")
    else:
        print(f"   Total action vector size: N/A")
    
    # Additional action space information
    if hasattr(env.action_space, 'n_line'):
        print(f"   • Number of power lines: {env.action_space.n_line}")
    if hasattr(env.action_space, 'n_sub'):
        print(f"   • Number of substations: {env.action_space.n_sub}")
    if hasattr(env.action_space, 'n_gen'):
        print(f"   • Number of generators: {env.action_space.n_gen}")
    if hasattr(env.action_space, 'n_load'):
        print(f"   • Number of loads: {env.action_space.n_load}")
    print()
    
    # Step 5: Reset environment and get initial observation
    print("🔄 Resetting Environment:")
    print("-" * 22)
    
    try:
        initial_obs = env.reset()
        obs_type = type(initial_obs).__name__
        
        print(f"✅ Environment reset successful!")
        print(f"🔬 Initial Observation Type: {obs_type}")
        
        # Additional observation details
        if hasattr(initial_obs, 'timestep'):
            print(f"   • Current timestep: {initial_obs.timestep}")
        if hasattr(initial_obs, 'max_step'):
            print(f"   • Maximum steps: {initial_obs.max_step}")
        if hasattr(initial_obs, 'current_step'):
            print(f"   • Current step: {initial_obs.current_step}")
        
        print()
        
    except Exception as e:
        print(f"❌ Error resetting environment: {e}")
        return
    
    # Step 6: Environment summary
    print("📋 Environment Summary:")
    print("-" * 20)
    print(f"✅ Grid2Op Environment '{env_params['env_name']}' is ready!")
    print(f"🚀 Backend: {type(env.backend).__name__}")
    print(f"📊 Observation space: {len(obs_attr_list)} attributes, shape {obs_shape}")
    print(f"🎮 Action space: {action_class_name}, shape {action_shape}")
    print(f"🔬 Initial observation: {obs_type}")
    print()
    print("🎯 Next steps:")
    print("   • Explore the observation and action spaces")
    print("   • Create and test different actions")
    print("   • Run simulation episodes")
    print("   • Develop reinforcement learning agents")
    print()

if __name__ == "__main__":
    main()
