#!/usr/bin/env python3
"""
Main Execution Script for Grid2Op PPO Agent Training and Evaluation

This script provides the main entry point for training and evaluating
Graph Capsule PPO agents on Grid2Op power grid topology optimization tasks.

Author: Grid Agent Project
Date: 2025-07-13
"""

import argparse
import os
import torch
import torch.nn as nn
import grid2op
from lightsim2grid import LightSimBackend
from grid2op.Agent import BaseAgent
from grid2op.Runner import Runner

# Import custom modules
from converters import GraphObservationConverter, ActionSpaceMapper
from custom_rewards import StabilityReward
from ppo_trainer import train_agent, create_default_hyperparameters
from gfl_agent_models import GraphCapsuleExtractor


class GraphReplayBuffer:
    """
    Placeholder for Graph Replay Buffer.
    TODO: Implement proper graph-based replay buffer.
    """
    def __init__(self, capacity: int, device: str = 'cpu'):
        self.capacity = capacity
        self.device = device
        print(f"📦 GraphReplayBuffer placeholder initialized (capacity: {capacity})")


class GcapsPPOAgent(nn.Module):
    """
    Placeholder for Graph Capsule PPO Agent.
    TODO: Implement complete PPO agent with actor-critic architecture.
    """
    def __init__(self, env, action_mapper, hyperparameters: dict, device: str = 'cpu'):
        super(GcapsPPOAgent, self).__init__()
        
        self.env = env
        self.action_mapper = action_mapper
        self.device = device
        self.action_space_size = action_mapper.action_space_size
        
        # Extract hyperparameters
        input_dim = hyperparameters.get('input_dim', 3)
        hidden_dim = hyperparameters.get('hidden_dim', 64)
        num_gcn_layers = hyperparameters.get('num_gcn_layers', 3)
        
        # Graph feature extractor
        self.feature_extractor = GraphCapsuleExtractor(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            num_gcn_layers=num_gcn_layers,
            num_input_capsules=16,
            num_output_capsules=8,
            input_capsule_dim=4,
            output_capsule_dim=8,
            num_routing_iterations=3
        )
        
        feature_dim = self.feature_extractor.get_output_dim()
        
        # Actor network (policy)
        self.actor = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, self.action_space_size)
        )
        
        # Critic network (value function)
        self.critic = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
        print(f"🤖 GcapsPPOAgent initialized:")
        print(f"   • Feature dim: {feature_dim}")
        print(f"   • Action space: {self.action_space_size}")
        print(f"   • Device: {device}")
    
    def forward(self, graph_data):
        """Extract features from graph data."""
        # Handle single graph vs batch of graphs
        if hasattr(graph_data, 'batch') and graph_data.batch is not None:
            # Already batched
            return self.feature_extractor(graph_data)
        else:
            # Single graph - create a batch of size 1
            from torch_geometric.data import Batch
            batch_data = Batch.from_data_list([graph_data])
            features = self.feature_extractor(batch_data)
            return features
    
    def get_action_and_value(self, graph_data):
        """
        Get action and value for a single graph.
        TODO: Implement proper action sampling and value estimation.
        """
        # Extract features from graph data
        features = self.forward(graph_data)

        # Ensure features have batch dimension
        if len(features.shape) == 1:
            features = features.unsqueeze(0)

        # Get action probabilities
        action_logits = self.actor(features)
        action_probs = torch.softmax(action_logits, dim=-1)

        # Sample action
        action_dist = torch.distributions.Categorical(action_probs)
        action = action_dist.sample()
        log_prob = action_dist.log_prob(action)

        # Get value
        value = self.critic(features)

        return action.squeeze(), log_prob.squeeze(), value.squeeze()
    
    def evaluate_actions(self, graph_states, actions):
        """
        Evaluate actions for batch of states.
        TODO: Implement proper batch evaluation.
        """
        # Placeholder implementation
        batch_size = len(graph_states)
        log_probs = torch.zeros(batch_size, device=self.device)
        values = torch.zeros(batch_size, device=self.device)
        entropy = torch.zeros(batch_size, device=self.device)
        
        return log_probs, values, entropy


class PytorchAgent(BaseAgent):
    """
    Wrapper agent class for Grid2Op evaluation using PyTorch models.
    
    This class encapsulates the PyTorch PPO agent and provides the Grid2Op
    BaseAgent interface for use with Grid2Op Runner and evaluation tools.
    """
    
    def __init__(self, model, converter, action_mapper, device='cpu'):
        """
        Initialize the PyTorch agent wrapper.
        
        Parameters
        ----------
        model : GcapsPPOAgent
            The trained PyTorch model
        converter : GraphObservationConverter
            Observation to graph converter
        action_mapper : ActionSpaceMapper
            Action index to Grid2Op action mapper
        device : str
            Device to run inference on
        """
        # Initialize with dummy action space (will be set properly)
        super(PytorchAgent, self).__init__(action_space=None)
        
        self.model = model
        self.converter = converter
        self.action_mapper = action_mapper
        self.device = device
        
        # Set model to evaluation mode
        self.model.eval()
        
        print(f"🎭 PytorchAgent wrapper initialized")
        print(f"   • Model: {type(model).__name__}")
        print(f"   • Device: {device}")
    
    def act(self, observation, reward, done=False):
        """
        Choose an action based on the current observation.
        
        Parameters
        ----------
        observation : grid2op.Observation.BaseObservation
            Current grid state observation
        reward : float
            Reward from previous action
        done : bool
            Whether episode has ended
            
        Returns
        -------
        grid2op.Action.BaseAction
            Action to take in the environment
        """
        
        try:
            # Step 1: Convert observation to graph data
            graph_data = self.converter.convert(observation)
            
            # Step 2: Get action from model
            with torch.no_grad():
                action_idx, _, _ = self.model.get_action_and_value(graph_data)
                action_idx = action_idx.item()
            
            # Step 3: Map to Grid2Op action
            grid2op_action = self.action_mapper.map_to_grid2op_action(action_idx)
            
            return grid2op_action
            
        except Exception as e:
            print(f"⚠️  Error in PytorchAgent.act(): {e}")
            # Fallback to do-nothing action
            return self.action_space()


def setup_device():
    """Setup the appropriate device for training/inference."""
    if torch.backends.mps.is_available():
        device = 'mps'
        print(f"🍎 Using MPS (Apple Silicon GPU) acceleration")
    elif torch.cuda.is_available():
        device = 'cuda'
        print(f"🚀 Using CUDA GPU acceleration")
    else:
        device = 'cpu'
        print(f"💻 Using CPU")
    
    return device


def create_hyperparameters():
    """Create hyperparameters for the agent and training."""
    hyperparams = create_default_hyperparameters()
    
    # Add model-specific hyperparameters
    hyperparams.update({
        'input_dim': 3,  # Node feature dimension
        'hidden_dim': 64,
        'num_gcn_layers': 3,
        'learning_rate': 3e-4
    })
    
    return hyperparams


def main():
    """Main execution function."""
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Grid2Op PPO Agent Training and Evaluation')
    parser.add_argument('--mode', choices=['train', 'evaluate'], required=True,
                       help='Mode: train or evaluate the agent')
    parser.add_argument('--model-path', type=str, default='models/gcaps_ppo_agent.pth',
                       help='Path to save/load the model')
    parser.add_argument('--episodes', type=int, default=10,
                       help='Number of episodes for evaluation')
    
    args = parser.parse_args()
    
    print(f"🎯 Grid2Op PPO Agent - Mode: {args.mode}")
    print("=" * 50)
    
    # Setup device
    device = setup_device()
    
    # Define hyperparameters
    hyperparameters = create_hyperparameters()
    
    # Create Grid2Op environment
    print(f"🏗️  Creating Grid2Op environment...")
    env = grid2op.make(
        "l2rpn_case14_sandbox",
        backend=LightSimBackend(),
        reward_class=StabilityReward
    )
    print(f"✅ Environment created: {env.name}")
    
    # Create converters
    print(f"🔄 Initializing converters...")
    converter = GraphObservationConverter(env, num_episodes=2, device=device)
    action_mapper = ActionSpaceMapper(env)
    
    # Create agent
    print(f"🤖 Creating GcapsPPOAgent...")
    agent = GcapsPPOAgent(env, action_mapper, hyperparameters, device=device)
    agent.to(device)
    
    if args.mode == 'train':
        print(f"🎓 Starting training mode...")
        
        # Create replay buffer
        replay_buffer = GraphReplayBuffer(capacity=hyperparameters['n_steps'], device=device)
        
        # Create optimizer
        optimizer = torch.optim.Adam(agent.parameters(), lr=hyperparameters['learning_rate'])
        
        # Train the agent
        train_agent(
            agent=agent,
            env=env,
            converter=converter,
            action_mapper=action_mapper,
            optimizer=optimizer,
            hyperparameters=hyperparameters,
            device=device
        )
        
        # Save the trained model
        os.makedirs(os.path.dirname(args.model_path), exist_ok=True)
        torch.save(agent.state_dict(), args.model_path)
        print(f"💾 Model saved to: {args.model_path}")
        
    elif args.mode == 'evaluate':
        print(f"📊 Starting evaluation mode...")
        
        # Load trained model if path exists
        if os.path.exists(args.model_path):
            agent.load_state_dict(torch.load(args.model_path, map_location=device))
            print(f"📂 Model loaded from: {args.model_path}")
        else:
            print(f"⚠️  Model file not found: {args.model_path}")
            print(f"   Using randomly initialized model for evaluation")
        
        # Create wrapper agent for Grid2Op
        pytorch_agent = PytorchAgent(agent, converter, action_mapper, device)
        
        # TODO: Implement evaluation with Grid2Op Runner
        print(f"🔄 Evaluation implementation coming in next prompt...")
        print(f"   Episodes to run: {args.episodes}")
        
        # Placeholder for evaluation
        # runner = Runner(**env.get_params_for_runner(), agentInstance=pytorch_agent)
        # results = runner.run(nb_episode=args.episodes)
    
    # Cleanup
    env.close()
    print(f"🎉 Execution completed!")


if __name__ == "__main__":
    main()
